# Moroccan Accounting Learning App (moroccanaccounting)

Version: 2.5.0+12

## Description

This comprehensive Flutter application is designed to help users master Moroccan accounting principles through an advanced learning ecosystem. It features interactive guides, adaptive quizzes, spaced repetition learning, advanced calculators, collaborative tools, and sophisticated analytics. The app covers all aspects of Moroccan accounting including General Accounting, Corporate Tax (IS), Personal Income Tax (IR), VAT (TVA), Fixed Assets, Provisions, Analytical Accounting, and professional financial analysis.

## 🚀 Key Features

### 📚 Core Learning Features
*   **Comprehensive Guides:** Detailed explanations and examples for various Moroccan accounting topics with interactive content
*   **Adaptive Quizzes:** Intelligent quiz system that adapts difficulty based on performance with real-time feedback
*   **Spaced Repetition System:** Scientifically-backed learning method for optimal knowledge retention
*   **Interactive Exams:** Comprehensive examination system with detailed performance analytics
*   **Progress Tracking:** Advanced progress monitoring with detailed analytics and insights

### 🧮 Professional Tools
*   **Advanced Calculators:** Professional-grade financial calculators including depreciation, ratios, and tax optimization
*   **Excel Export:** Export calculations and reports to Excel format for professional use
*   **Financial Analysis:** Comprehensive financial ratio analysis and interpretation tools
*   **Tax Optimization:** Advanced tax planning and optimization calculators

### 🤝 Collaboration & Social Features
*   **Collaborative Notes:** Create, share, and collaborate on study notes with other users
*   **Social Learning:** Connect with other learners, share progress, and learn together
*   **Professional Networking:** Connect with accounting professionals and peers
*   **Real-time Collaboration:** Live collaboration features for group study sessions

### 🎯 Gamification & Engagement
*   **Achievement System:** Comprehensive achievement and badge system to motivate learning
*   **Daily Goals:** Personalized daily learning goals and streak tracking
*   **Leaderboards:** Compete with other learners in various categories
*   **Learning Streaks:** Track and maintain learning consistency

### 📊 Analytics & Insights
*   **Performance Analytics:** Detailed analysis of learning performance and progress trends
*   **Feature Usage Analytics:** Insights into app usage patterns and feature adoption
*   **Learning Insights:** Personalized recommendations based on learning patterns
*   **Advanced Reporting:** Comprehensive reports on learning progress and achievements

### ♿ Accessibility & Inclusivity
*   **Screen Reader Support:** Full compatibility with screen readers and assistive technologies
*   **Voice Commands:** Voice-controlled navigation and interaction
*   **High Contrast Mode:** Enhanced visibility options for users with visual impairments
*   **Keyboard Navigation:** Complete keyboard navigation support
*   **Multi-language Support:** Support for multiple languages including Arabic and French

### 🔧 Advanced Technical Features
*   **Offline Support:** Full offline functionality with intelligent synchronization
*   **Performance Optimization:** Advanced caching, lazy loading, and memory management
*   **Data Compression:** Intelligent data compression for optimal storage and performance
*   **Background Sync:** Seamless background synchronization across devices
*   **Cross-platform:** Optimized for mobile, tablet, and desktop platforms

## Project Structure Overview

The project follows a standard Flutter structure, organized by feature/layer:

*   `lib/`: Contains the core Dart code.
    *   `main.dart`: Application entry point, initialization, and routing.
    *   `models/`: Data models, including Hive models (`user_profile.dart`, `quiz_attempt.dart`, `user_progress.dart`, `active_quiz_state.dart`) for local persistence.
    *   `screens/`: UI screens for different parts of the app (e.g., `home_screen.dart`, `quiz_screen.dart`, `profile_screen.dart`, specific guide screens).
    *   `services/`: Business logic, data fetching/processing (e.g., `quiz_data_service.dart`, `user_progress_service.dart`, `theme_service.dart`).
    *   `providers/`: State management providers (using Riverpod/Provider) to make data and services available to the UI.
    *   `controllers/`: Controllers (e.g., `quiz_controller.dart`) likely managing UI logic and state interaction.
    *   `widgets/`: Reusable UI components (Implicit, common practice).
*   `assets/`: Static assets used by the application.
    *   `compta_generale/`, `is/`, `ir/`, `tva/`, etc.: JSON files containing the accounting content and quiz data.
    *   `images/`: Image assets.
    *   `sounds/`: Sound effects for quizzes.
    *   `fonts/`: Custom fonts (Roboto).

## Getting Started

### Prerequisites

*   Flutter SDK: Ensure you have Flutter installed. See [Flutter installation guide](https://docs.flutter.dev/get-started/install).
*   Dart SDK: Comes with Flutter.
*   An IDE like VS Code or Android Studio with Flutter plugins.

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd moroccanaccounting
    ```
2.  **Install dependencies:**
    ```bash
    flutter pub get
    ```
3.  **Generate Code (if necessary):**
    The project uses code generation (e.g., for Hive, Freezed). If you modify models, you might need to run the build runner:
    ```bash
    flutter pub run build_runner build --delete-conflicting-outputs
    ```

### Running the App

1.  **Connect a device or start an emulator/simulator.**
2.  **Run the app:**
    ```bash
    flutter run
    ```

## Usage

Launch the application to access the home screen. From there, navigate to:
*   **Guides:** Browse various accounting topics.
*   **Quizzes:** Test your understanding of the selected topics.
*   **Profile:** View your progress and potentially manage settings.

## Dependencies

Key packages used:

*   `flutter_riverpod` / `provider`: State management
*   `hive` / `hive_flutter`: Local database storage
*   `intl`: Internationalization and formatting
*   `google_fonts`: Custom fonts
*   `fl_chart` / `syncfusion_flutter_charts`: Charting
*   `just_audio`: Audio playback
*   `lottie` / `flutter_animate`: Animations
*   `freezed` / `json_serializable` / `hive_generator`: Code generation

(See `pubspec.yaml` for the full list.)
