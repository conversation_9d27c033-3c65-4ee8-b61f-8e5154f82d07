import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';

class FusionWidget extends StatelessWidget {
  final Map<String, dynamic> societeA;
  final Map<String, dynamic> societeB;
  final String type;
  final DateTime date;

  const FusionWidget({
    super.key,
    required this.societeA,
    required this.societeB,
    required this.type,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: isDark ? 0.1 : 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(15),
              ),
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.merge_type_rounded,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fusion ${type.toUpperCase()}',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.primary.withValues(alpha: 0.8),
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildCompanyCard(
                      context,
                      'Société A',
                      societeA,
                      Icons.business_rounded,
                    ),
                    const SizedBox(width: 16),
                    _buildCompanyCard(
                      context,
                      'Société B',
                      societeB,
                      Icons.business_center_rounded,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSection(
                  context,
                  'Calcul de la Parité d\'Échange',
                  _buildParityCalculation(),
                  Icons.calculate_rounded,
                ),
                const SizedBox(height: 24),
                _buildSection(
                  context,
                  'Prime de Fusion',
                  _buildFusionPremium(),
                  Icons.trending_up_rounded,
                ),
                const SizedBox(height: 24),
                _buildSection(
                  context,
                  'Écritures Comptables',
                  _buildAccountingEntries(),
                  Icons.edit_note_rounded,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyCard(
    BuildContext context,
    String title,
    Map<String, dynamic> data,
    IconData icon,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final capital = data['capital'] as double;
    final reserves = data['reserves'] as double;
    final plusValue = data['plus_value'] as double;
    final totalValue = capital + reserves + plusValue;
    final shares = data['actions'] as int;
    final shareValue = totalValue / shares;

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: textTheme.titleSmall?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildValueRow(context, 'Capital', capital),
            const SizedBox(height: 8),
            _buildValueRow(context, 'Réserves', reserves),
            const SizedBox(height: 8),
            _buildValueRow(context, 'Plus-value', plusValue),
            const Divider(height: 16),
            _buildValueRow(context, 'Total', totalValue, highlight: true),
            const SizedBox(height: 8),
            _buildValueRow(context, 'Actions', shares.toDouble(), isShares: true),
            const SizedBox(height: 8),
            _buildValueRow(context, 'Valeur/Action', shareValue, highlight: true),
          ],
        ),
      ),
    );
  }

  Widget _buildValueRow(
    BuildContext context,
    String label,
    double value, {
    bool highlight = false,
    bool isShares = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    String formattedValue;
    if (isShares) {
      formattedValue = NumberFormat('#,##0', 'fr_FR').format(value);
    } else {
      formattedValue = '${NumberFormat('#,##0.00', 'fr_FR').format(value)} DH';
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          formattedValue,
          style: (highlight ? textTheme.titleSmall : textTheme.bodySmall)?.copyWith(
            color: highlight ? colorScheme.primary : colorScheme.onSurface,
            fontWeight: highlight ? FontWeight.bold : null,
          ),
        ),
      ],
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    Widget content,
    IconData icon,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildParityCalculation() {
    return const Text('TODO: Implement parity calculation');
  }

  Widget _buildFusionPremium() {
    return const Text('TODO: Implement fusion premium calculation');
  }

  Widget _buildAccountingEntries() {
    return const Text('TODO: Implement accounting entries');
  }
}
