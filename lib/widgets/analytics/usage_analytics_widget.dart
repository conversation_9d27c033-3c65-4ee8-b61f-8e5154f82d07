import 'package:flutter/material.dart';

import '../../services/feature_analytics_service.dart';

/// Analytics widgets that visualize feature usage and app performance
/// Displays charts showing feature usage patterns, engagement trends, and performance metrics
class UsageAnalyticsWidget extends StatefulWidget {
  final FeatureAnalyticsService analyticsService;
  final bool showDetailedMetrics;
  final Duration refreshInterval;

  const UsageAnalyticsWidget({
    super.key,
    required this.analyticsService,
    this.showDetailedMetrics = true,
    this.refreshInterval = const Duration(minutes: 5),
  });

  @override
  State<UsageAnalyticsWidget> createState() => _UsageAnalyticsWidgetState();
}

class _UsageAnalyticsWidgetState extends State<UsageAnalyticsWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _refreshController;
  
  Map<String, dynamic> _analyticsData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _refreshController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _loadAnalyticsData();
    _setupPeriodicRefresh();
  }

  void _setupPeriodicRefresh() {
    Stream.periodic(widget.refreshInterval).listen((_) {
      if (mounted) {
        _loadAnalyticsData();
      }
    });
  }

  Future<void> _loadAnalyticsData() async {
    if (mounted) {
      setState(() => _isLoading = true);
    }

    try {
      final data = widget.analyticsService.getAnalyticsReport();
      if (mounted) {
        setState(() {
          _analyticsData = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading analytics data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: Column(
        children: [
          _buildHeader(theme, colorScheme),
          if (_isLoading)
            const Expanded(
              child: Center(child: CircularProgressIndicator()),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildFeatureUsageTab(),
                  _buildEngagementTab(),
                  _buildInsightsTab(),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                'Analytics d\'Utilisation',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _refreshData,
                icon: AnimatedBuilder(
                  animation: _refreshController,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _refreshController.value * 2 * 3.14159,
                      child: const Icon(Icons.refresh),
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            labelColor: colorScheme.primary,
            unselectedLabelColor: colorScheme.onSurfaceVariant,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: const [
              Tab(text: 'Vue d\'ensemble'),
              Tab(text: 'Fonctionnalités'),
              Tab(text: 'Engagement'),
              Tab(text: 'Insights'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final featureUsage = _analyticsData['feature_usage'] as Map<String, dynamic>? ?? {};
    final counts = featureUsage['counts'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricsGrid(),
          const SizedBox(height: 24),
          _buildUsageOverviewChart(counts),
          const SizedBox(height: 24),
          _buildRecentActivityList(),
        ],
      ),
    );
  }

  Widget _buildFeatureUsageTab() {
    final categoryAnalysis = _analyticsData['category_analysis'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildCategoryUsageChart(categoryAnalysis),
          const SizedBox(height: 24),
          _buildFeatureAdoptionChart(),
          const SizedBox(height: 24),
          _buildUnderutilizedFeaturesList(),
        ],
      ),
    );
  }

  Widget _buildEngagementTab() {
    final engagement = _analyticsData['engagement'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildEngagementScoreChart(engagement),
          const SizedBox(height: 24),
          _buildUserBehaviorChart(),
          const SizedBox(height: 24),
          _buildRetentionMetrics(),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    final recommendations = _analyticsData['recommendations'] as List? ?? [];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildRecommendationsList(recommendations),
          const SizedBox(height: 24),
          _buildTrendAnalysis(),
          const SizedBox(height: 24),
          _buildPerformanceInsights(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    final metrics = [
      {'title': 'Fonctionnalités Utilisées', 'value': '12/18', 'icon': Icons.featured_play_list, 'color': Colors.blue},
      {'title': 'Engagement Moyen', 'value': '7.2/10', 'icon': Icons.trending_up, 'color': Colors.green},
      {'title': 'Sessions Aujourd\'hui', 'value': '3', 'icon': Icons.access_time, 'color': Colors.orange},
      {'title': 'Temps Total', 'value': '2h 15m', 'icon': Icons.schedule, 'color': Colors.purple},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return _buildMetricCard(
          title: metric['title'] as String,
          value: metric['value'] as String,
          icon: metric['icon'] as IconData,
          color: metric['color'] as Color,
        );
      },
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: 16),
                ),
                const Spacer(),
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageOverviewChart(Map<String, dynamic> counts) {
    if (counts.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(child: Text('Aucune donnée d\'utilisation disponible')),
      );
    }

    return SizedBox(
      height: 200,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Aperçu de l\'Utilisation',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: counts.entries.length,
                  itemBuilder: (context, index) {
                    final entry = counts.entries.elementAt(index);
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: _getColorForFeature(entry.key),
                        radius: 8,
                      ),
                      title: Text(entry.key),
                      trailing: Text('${entry.value}'),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryUsageChart(Map<String, dynamic> categoryAnalysis) {
    if (categoryAnalysis.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(child: Text('Aucune donnée de catégorie disponible')),
      );
    }

    return SizedBox(
      height: 200,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Utilisation par Catégorie',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: categoryAnalysis.entries.length,
                  itemBuilder: (context, index) {
                    final entry = categoryAnalysis.entries.elementAt(index);
                    final categoryData = entry.value as Map<String, dynamic>;
                    final adoptionRate = categoryData['adoption_rate'] as num? ?? 0;

                    return ListTile(
                      leading: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: _getColorForCategory(entry.key),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      title: Text(entry.key),
                      trailing: Text('${adoptionRate.toStringAsFixed(1)}%'),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEngagementScoreChart(Map<String, dynamic> engagement) {
    // Implementation for engagement score visualization
    return const SizedBox(
      height: 200,
      child: Center(child: Text('Graphique d\'engagement')),
    );
  }

  Widget _buildFeatureAdoptionChart() {
    // Implementation for feature adoption over time
    return const SizedBox(
      height: 200,
      child: Center(child: Text('Graphique d\'adoption des fonctionnalités')),
    );
  }

  Widget _buildUnderutilizedFeaturesList() {
    final underutilized = _analyticsData['underutilized_features'] as List? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fonctionnalités Sous-utilisées',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...underutilized.take(5).map((feature) => ListTile(
          leading: Icon(Icons.lightbulb_outline, color: Colors.orange),
          title: Text(feature.toString()),
          subtitle: const Text('Recommandé pour vous'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () => _navigateToFeature(feature.toString()),
        )),
      ],
    );
  }

  Widget _buildUserBehaviorChart() {
    // Implementation for user behavior patterns
    return const SizedBox(
      height: 200,
      child: Center(child: Text('Graphique de comportement utilisateur')),
    );
  }

  Widget _buildRetentionMetrics() {
    // Implementation for retention metrics
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Métriques de Rétention', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('Taux de retour: 85%'),
            Text('Sessions par jour: 2.3'),
            Text('Durée moyenne: 18 min'),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationsList(List recommendations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recommandations Personnalisées',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...recommendations.take(3).map((rec) => Card(
          child: ListTile(
            leading: const Icon(Icons.recommend, color: Colors.blue),
            title: Text(rec.toString()),
            subtitle: const Text('Basé sur votre utilisation'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          ),
        )),
      ],
    );
  }

  Widget _buildTrendAnalysis() {
    // Implementation for trend analysis
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Analyse des Tendances', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('📈 Utilisation en hausse: +15% cette semaine'),
            Text('🎯 Fonctionnalité populaire: Quiz interactifs'),
            Text('⏰ Pic d\'activité: 19h-21h'),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceInsights() {
    // Implementation for performance insights
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Insights Performance', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('⚡ Temps de chargement: 1.2s'),
            Text('💾 Utilisation mémoire: Optimale'),
            Text('🔄 Taux de synchronisation: 98%'),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivityList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Activité Récente',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...List.generate(3, (index) => ListTile(
          leading: CircleAvatar(
            backgroundColor: Colors.blue.withValues(alpha: 0.1),
            child: const Icon(Icons.history, color: Colors.blue),
          ),
          title: Text('Activité ${index + 1}'),
          subtitle: Text('Il y a ${index + 1} heure${index > 0 ? 's' : ''}'),
          trailing: const Icon(Icons.more_vert),
        )),
      ],
    );
  }

  Color _getColorForFeature(String feature) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.red, Colors.teal];
    return colors[feature.hashCode % colors.length];
  }

  Color _getColorForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'learning': return Colors.blue;
      case 'social': return Colors.green;
      case 'analytics': return Colors.purple;
      case 'calculators': return Colors.orange;
      case 'gamification': return Colors.red;
      default: return Colors.grey;
    }
  }

  void _refreshData() {
    _refreshController.forward().then((_) {
      _refreshController.reset();
    });
    _loadAnalyticsData();
  }

  void _navigateToFeature(String feature) {
    // Implementation to navigate to specific feature
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Navigation vers: $feature')),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    super.dispose();
  }
}
