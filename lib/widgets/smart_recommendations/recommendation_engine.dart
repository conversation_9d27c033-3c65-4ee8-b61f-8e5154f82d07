import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../services/adaptive_learning_service.dart';
import '../../services/personalized_learning_service.dart';

/// Intelligent recommendation engine for personalized content and features
/// Uses machine learning algorithms for content recommendations and difficulty adjustment
class RecommendationEngine extends ChangeNotifier {
  final AdaptiveLearningService _adaptiveLearningService;
  final PersonalizedLearningService _personalizedLearningService;
  
  // User behavior tracking
  final Map<String, double> _userPreferences = {};
  final Map<String, int> _featureUsageCount = {};
  final Map<String, DateTime> _lastFeatureUsage = {};
  final List<UserInteraction> _interactionHistory = [];
  
  // Recommendation models
  final Map<String, RecommendationModel> _models = {};
  
  // Content categories and weights
  static const Map<String, double> _categoryWeights = {
    'comptabilite': 1.0,
    'fiscalite': 1.0,
    'calculators': 0.8,
    'quizzes': 0.9,
    'guides': 0.7,
    'analytics': 0.6,
    'gamification': 0.5,
  };
  
  RecommendationEngine({
    required AdaptiveLearningService adaptiveLearningService,
    required PersonalizedLearningService personalizedLearningService,
  }) : _adaptiveLearningService = adaptiveLearningService,
       _personalizedLearningService = personalizedLearningService {
    _initializeModels();
  }
  
  /// Initialize recommendation models
  void _initializeModels() {
    _models['content'] = ContentRecommendationModel();
    _models['difficulty'] = DifficultyRecommendationModel();
    _models['feature'] = FeatureRecommendationModel();
    _models['schedule'] = ScheduleRecommendationModel();
  }
  
  /// Track user interaction for learning
  void trackInteraction(UserInteraction interaction) {
    _interactionHistory.add(interaction);
    
    // Update feature usage
    _featureUsageCount[interaction.feature] = 
        (_featureUsageCount[interaction.feature] ?? 0) + 1;
    _lastFeatureUsage[interaction.feature] = interaction.timestamp;
    
    // Update preferences based on interaction
    _updateUserPreferences(interaction);
    
    // Limit history size
    if (_interactionHistory.length > 1000) {
      _interactionHistory.removeRange(0, _interactionHistory.length - 1000);
    }
    
    // Retrain models periodically
    if (_interactionHistory.length % 50 == 0) {
      _retrainModels();
    }
    
    notifyListeners();
  }
  
  /// Update user preferences based on interaction
  void _updateUserPreferences(UserInteraction interaction) {
    final category = _getCategoryFromFeature(interaction.feature);
    final currentPreference = _userPreferences[category] ?? 0.5;
    
    // Adjust preference based on interaction quality
    double adjustment = 0.0;
    switch (interaction.type) {
      case InteractionType.positive:
        adjustment = 0.1 * interaction.intensity;
        break;
      case InteractionType.negative:
        adjustment = -0.05 * interaction.intensity;
        break;
      case InteractionType.neutral:
        adjustment = 0.01;
        break;
    }
    
    _userPreferences[category] = 
        (currentPreference + adjustment).clamp(0.0, 1.0);
  }
  
  /// Get category from feature name
  String _getCategoryFromFeature(String feature) {
    if (feature.contains('quiz')) return 'quizzes';
    if (feature.contains('calculator')) return 'calculators';
    if (feature.contains('guide')) return 'guides';
    if (feature.contains('analytics')) return 'analytics';
    if (feature.contains('gamification')) return 'gamification';
    if (feature.contains('fiscalite')) return 'fiscalite';
    return 'comptabilite';
  }
  
  /// Generate content recommendations
  List<ContentRecommendation> getContentRecommendations({
    int limit = 5,
    String? category,
    double? difficultyLevel,
  }) {
    final model = _models['content'] as ContentRecommendationModel;
    
    return model.generateRecommendations(
      userPreferences: _userPreferences,
      interactionHistory: _interactionHistory,
      limit: limit,
      category: category,
      difficultyLevel: difficultyLevel,
    );
  }
  
  /// Generate feature recommendations
  List<FeatureRecommendation> getFeatureRecommendations({
    int limit = 3,
  }) {
    final model = _models['feature'] as FeatureRecommendationModel;
    
    return model.generateRecommendations(
      featureUsage: _featureUsageCount,
      lastUsage: _lastFeatureUsage,
      userPreferences: _userPreferences,
      limit: limit,
    );
  }
  
  /// Generate difficulty recommendations
  DifficultyRecommendation getDifficultyRecommendation(String contentType) {
    final model = _models['difficulty'] as DifficultyRecommendationModel;
    
    return model.generateRecommendation(
      contentType: contentType,
      interactionHistory: _interactionHistory,
      userPreferences: _userPreferences,
    );
  }
  
  /// Generate optimal study schedule
  StudyScheduleRecommendation getStudyScheduleRecommendation() {
    final model = _models['schedule'] as ScheduleRecommendationModel;
    
    return model.generateRecommendation(
      interactionHistory: _interactionHistory,
      userPreferences: _userPreferences,
    );
  }
  
  /// Get personalized learning path
  List<LearningPathStep> getPersonalizedLearningPath({
    String? targetTopic,
    int maxSteps = 10,
  }) {
    final steps = <LearningPathStep>[];
    
    // Analyze user's current knowledge level
    final knowledgeMap = _analyzeKnowledgeLevel();
    
    // Identify weak areas
    final weakAreas = _identifyWeakAreas(knowledgeMap);
    
    // Generate learning path
    for (final area in weakAreas.take(maxSteps)) {
      final difficulty = getDifficultyRecommendation(area);
      final content = getContentRecommendations(
        category: area,
        difficultyLevel: difficulty.level,
        limit: 3,
      );
      
      steps.add(LearningPathStep(
        topic: area,
        difficulty: difficulty.level,
        estimatedDuration: Duration(minutes: 15 + (difficulty.level * 10).round()),
        content: content,
        priority: _calculatePriority(area, knowledgeMap[area] ?? 0.0),
      ));
    }
    
    // Sort by priority
    steps.sort((a, b) => b.priority.compareTo(a.priority));
    
    return steps;
  }
  
  /// Analyze user's knowledge level across topics
  Map<String, double> _analyzeKnowledgeLevel() {
    final knowledgeMap = <String, double>{};
    
    for (final category in _categoryWeights.keys) {
      final interactions = _interactionHistory
          .where((i) => _getCategoryFromFeature(i.feature) == category)
          .toList();
      
      if (interactions.isEmpty) {
        knowledgeMap[category] = 0.0;
        continue;
      }
      
      // Calculate knowledge score based on recent interactions
      double totalScore = 0.0;
      double totalWeight = 0.0;
      
      for (final interaction in interactions.take(20)) {
        final recency = _calculateRecencyWeight(interaction.timestamp);
        final score = interaction.type == InteractionType.positive 
            ? interaction.intensity 
            : -interaction.intensity * 0.5;
        
        totalScore += score * recency;
        totalWeight += recency;
      }
      
      knowledgeMap[category] = totalWeight > 0 
          ? (totalScore / totalWeight).clamp(0.0, 1.0)
          : 0.0;
    }
    
    return knowledgeMap;
  }
  
  /// Identify weak areas that need improvement
  List<String> _identifyWeakAreas(Map<String, double> knowledgeMap) {
    final weakAreas = knowledgeMap.entries
        .where((entry) => entry.value < 0.6)
        .map((entry) => entry.key)
        .toList();
    
    // Sort by knowledge level (lowest first)
    weakAreas.sort((a, b) => knowledgeMap[a]!.compareTo(knowledgeMap[b]!));
    
    return weakAreas;
  }
  
  /// Calculate recency weight for interactions
  double _calculateRecencyWeight(DateTime timestamp) {
    final daysSince = DateTime.now().difference(timestamp).inDays;
    return exp(-daysSince / 7.0); // Exponential decay over weeks
  }
  
  /// Calculate priority for learning path step
  double _calculatePriority(String topic, double knowledgeLevel) {
    final categoryWeight = _categoryWeights[topic] ?? 0.5;
    final urgency = 1.0 - knowledgeLevel; // Lower knowledge = higher urgency
    final preference = _userPreferences[topic] ?? 0.5;
    
    return (urgency * 0.5 + categoryWeight * 0.3 + preference * 0.2);
  }
  
  /// Retrain recommendation models
  void _retrainModels() {
    try {
      for (final model in _models.values) {
        model.train(_interactionHistory, _userPreferences);
      }
      debugPrint('Recommendation models retrained');
    } catch (e) {
      debugPrint('Error retraining models: $e');
    }
  }
  
  /// Get recommendation insights
  Map<String, dynamic> getRecommendationInsights() {
    return {
      'user_preferences': Map.from(_userPreferences),
      'feature_usage': Map.from(_featureUsageCount),
      'interaction_count': _interactionHistory.length,
      'knowledge_levels': _analyzeKnowledgeLevel(),
      'weak_areas': _identifyWeakAreas(_analyzeKnowledgeLevel()),
      'last_model_training': DateTime.now().toIso8601String(),
    };
  }
}

/// Represents a user interaction for learning
class UserInteraction {
  final String feature;
  final InteractionType type;
  final double intensity; // 0.0 to 1.0
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  UserInteraction({
    required this.feature,
    required this.type,
    required this.intensity,
    required this.timestamp,
    this.metadata,
  });
}

enum InteractionType { positive, negative, neutral }

/// Base class for recommendation models
abstract class RecommendationModel {
  void train(List<UserInteraction> interactions, Map<String, double> preferences);
}

/// Content recommendation model
class ContentRecommendationModel extends RecommendationModel {
  @override
  void train(List<UserInteraction> interactions, Map<String, double> preferences) {
    // Implement content-based filtering
  }
  
  List<ContentRecommendation> generateRecommendations({
    required Map<String, double> userPreferences,
    required List<UserInteraction> interactionHistory,
    required int limit,
    String? category,
    double? difficultyLevel,
  }) {
    // Simplified recommendation logic
    final recommendations = <ContentRecommendation>[];
    
    final categories = category != null ? [category] : userPreferences.keys.toList();
    
    for (final cat in categories) {
      final preference = userPreferences[cat] ?? 0.5;
      if (preference > 0.3) {
        recommendations.add(ContentRecommendation(
          id: '${cat}_content_${recommendations.length}',
          title: 'Contenu recommandé: $cat',
          category: cat,
          difficulty: difficultyLevel ?? preference,
          score: preference,
          reason: 'Basé sur vos préférences',
        ));
      }
    }
    
    return recommendations.take(limit).toList();
  }
}

/// Feature recommendation model
class FeatureRecommendationModel extends RecommendationModel {
  @override
  void train(List<UserInteraction> interactions, Map<String, double> preferences) {
    // Implement collaborative filtering
  }
  
  List<FeatureRecommendation> generateRecommendations({
    required Map<String, int> featureUsage,
    required Map<String, DateTime> lastUsage,
    required Map<String, double> userPreferences,
    required int limit,
  }) {
    final recommendations = <FeatureRecommendation>[];
    
    // Recommend underused features
    final allFeatures = [
      'spaced_repetition',
      'adaptive_learning',
      'analytics',
      'calculators',
      'collaboration',
    ];
    
    for (final feature in allFeatures) {
      final usage = featureUsage[feature] ?? 0;
      if (usage < 5) { // Underused
        recommendations.add(FeatureRecommendation(
          feature: feature,
          title: 'Découvrez: $feature',
          description: 'Cette fonctionnalité pourrait vous être utile',
          score: 1.0 - (usage / 10.0),
          reason: 'Fonctionnalité peu utilisée',
        ));
      }
    }
    
    return recommendations.take(limit).toList();
  }
}

/// Difficulty recommendation model
class DifficultyRecommendationModel extends RecommendationModel {
  @override
  void train(List<UserInteraction> interactions, Map<String, double> preferences) {
    // Implement difficulty adjustment algorithm
  }
  
  DifficultyRecommendation generateRecommendation({
    required String contentType,
    required List<UserInteraction> interactionHistory,
    required Map<String, double> userPreferences,
  }) {
    final relevantInteractions = interactionHistory
        .where((i) => i.feature.contains(contentType))
        .take(10)
        .toList();
    
    if (relevantInteractions.isEmpty) {
      return DifficultyRecommendation(
        level: 0.5,
        confidence: 0.3,
        reason: 'Niveau débutant recommandé',
      );
    }
    
    final avgPerformance = relevantInteractions
        .map((i) => i.type == InteractionType.positive ? i.intensity : -i.intensity)
        .reduce((a, b) => a + b) / relevantInteractions.length;
    
    final adjustedLevel = (0.5 + avgPerformance * 0.3).clamp(0.1, 0.9);
    
    return DifficultyRecommendation(
      level: adjustedLevel,
      confidence: 0.8,
      reason: 'Basé sur vos performances récentes',
    );
  }
}

/// Schedule recommendation model
class ScheduleRecommendationModel extends RecommendationModel {
  @override
  void train(List<UserInteraction> interactions, Map<String, double> preferences) {
    // Implement schedule optimization
  }
  
  StudyScheduleRecommendation generateRecommendation({
    required List<UserInteraction> interactionHistory,
    required Map<String, double> userPreferences,
  }) {
    // Analyze usage patterns
    final hourlyUsage = <int, int>{};
    for (final interaction in interactionHistory) {
      final hour = interaction.timestamp.hour;
      hourlyUsage[hour] = (hourlyUsage[hour] ?? 0) + 1;
    }
    
    // Find peak usage hours
    final peakHours = hourlyUsage.entries
        .where((entry) => entry.value > 2)
        .map((entry) => entry.key)
        .toList();
    
    return StudyScheduleRecommendation(
      optimalHours: peakHours,
      sessionDuration: const Duration(minutes: 25),
      breakDuration: const Duration(minutes: 5),
      reason: 'Basé sur vos habitudes d\'utilisation',
    );
  }
}

// Recommendation data classes
class ContentRecommendation {
  final String id;
  final String title;
  final String category;
  final double difficulty;
  final double score;
  final String reason;

  ContentRecommendation({
    required this.id,
    required this.title,
    required this.category,
    required this.difficulty,
    required this.score,
    required this.reason,
  });
}

class FeatureRecommendation {
  final String feature;
  final String title;
  final String description;
  final double score;
  final String reason;

  FeatureRecommendation({
    required this.feature,
    required this.title,
    required this.description,
    required this.score,
    required this.reason,
  });
}

class DifficultyRecommendation {
  final double level;
  final double confidence;
  final String reason;

  DifficultyRecommendation({
    required this.level,
    required this.confidence,
    required this.reason,
  });
}

class StudyScheduleRecommendation {
  final List<int> optimalHours;
  final Duration sessionDuration;
  final Duration breakDuration;
  final String reason;

  StudyScheduleRecommendation({
    required this.optimalHours,
    required this.sessionDuration,
    required this.breakDuration,
    required this.reason,
  });
}

class LearningPathStep {
  final String topic;
  final double difficulty;
  final Duration estimatedDuration;
  final List<ContentRecommendation> content;
  final double priority;

  LearningPathStep({
    required this.topic,
    required this.difficulty,
    required this.estimatedDuration,
    required this.content,
    required this.priority,
  });
}
