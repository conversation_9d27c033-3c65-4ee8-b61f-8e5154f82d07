import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Interactive feature discovery system with guided tours and tooltips
/// Provides step-by-step walkthroughs for complex features
class FeatureDiscoveryOverlay extends StatefulWidget {
  final Widget child;
  final List<FeatureDiscoveryStep> steps;
  final VoidCallback? onComplete;
  final bool autoStart;
  final String? tourId;

  const FeatureDiscoveryOverlay({
    super.key,
    required this.child,
    required this.steps,
    this.onComplete,
    this.autoStart = false,
    this.tourId,
  });

  @override
  State<FeatureDiscoveryOverlay> createState() => _FeatureDiscoveryOverlayState();

  /// Start a feature discovery tour
  static void startTour(BuildContext context, String tourId) {
    final state = context.findAncestorStateOfType<_FeatureDiscoveryOverlayState>();
    state?._startTour();
  }

  /// Show a single feature highlight
  static void showFeature(BuildContext context, String featureKey) {
    final state = context.findAncestorStateOfType<_FeatureDiscoveryOverlayState>();
    state?._showSingleFeature(featureKey);
  }
}

class _FeatureDiscoveryOverlayState extends State<FeatureDiscoveryOverlay>
    with TickerProviderStateMixin {
  OverlayEntry? _overlayEntry;
  int _currentStepIndex = 0;
  bool _isActive = false;
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startTour();
      });
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _startTour() {
    if (widget.steps.isEmpty || _isActive) return;

    setState(() {
      _isActive = true;
      _currentStepIndex = 0;
    });

    _showCurrentStep();
  }

  void _showSingleFeature(String featureKey) {
    final step = widget.steps.firstWhere(
      (step) => step.key == featureKey,
      orElse: () => widget.steps.first,
    );

    _showStep(step, isSingleFeature: true);
  }

  void _showCurrentStep() {
    if (_currentStepIndex >= widget.steps.length) {
      _completeTour();
      return;
    }

    final step = widget.steps[_currentStepIndex];
    _showStep(step);
  }

  void _showStep(FeatureDiscoveryStep step, {bool isSingleFeature = false}) {
    _removeOverlay();

    // Wait for the widget to be built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final targetContext = step.targetKey.currentContext;
      if (targetContext == null) {
        if (!isSingleFeature) _nextStep();
        return;
      }

      final renderBox = targetContext.findRenderObject() as RenderBox;
      final targetSize = renderBox.size;
      final targetPosition = renderBox.localToGlobal(Offset.zero);

      _overlayEntry = OverlayEntry(
        builder: (context) => _buildOverlay(
          step,
          targetPosition,
          targetSize,
          isSingleFeature,
        ),
      );

      Overlay.of(context).insert(_overlayEntry!);
      _animationController.forward();

      // Auto-advance for certain step types
      if (step.autoAdvance && !isSingleFeature) {
        Timer(step.duration ?? const Duration(seconds: 3), () {
          _nextStep();
        });
      }
    });
  }

  Widget _buildOverlay(
    FeatureDiscoveryStep step,
    Offset targetPosition,
    Size targetSize,
    bool isSingleFeature,
  ) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Material(
            color: Colors.black.withValues(alpha: 0.7),
            child: Stack(
              children: [
                // Background tap to dismiss
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () => isSingleFeature ? _removeOverlay() : _skipTour(),
                    child: Container(color: Colors.transparent),
                  ),
                ),
                // Highlight hole
                _buildHighlightHole(targetPosition, targetSize),
                // Pulsing highlight border
                _buildPulsingBorder(targetPosition, targetSize),
                // Tooltip
                _buildTooltip(step, targetPosition, targetSize, isSingleFeature),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHighlightHole(Offset position, Size size) {
    return ClipPath(
      clipper: _HoleClipper(
        holeRect: Rect.fromLTWH(
          position.dx - 8,
          position.dy - 8,
          size.width + 16,
          size.height + 16,
        ),
      ),
      child: Container(
        color: Colors.black.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildPulsingBorder(Offset position, Size size) {
    return Positioned(
      left: position.dx - 8,
      top: position.dy - 8,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              width: size.width + 16,
              height: size.height + 16,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTooltip(
    FeatureDiscoveryStep step,
    Offset targetPosition,
    Size targetSize,
    bool isSingleFeature,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final tooltipWidth = 280.0;
    final tooltipHeight = 120.0;

    // Calculate tooltip position
    double left = targetPosition.dx + targetSize.width + 16;
    double top = targetPosition.dy;

    // Adjust if tooltip goes off screen
    if (left + tooltipWidth > screenSize.width) {
      left = targetPosition.dx - tooltipWidth - 16;
    }
    if (top + tooltipHeight > screenSize.height) {
      top = screenSize.height - tooltipHeight - 16;
    }
    if (top < 16) top = 16;

    return Positioned(
      left: left,
      top: top,
      child: Transform.scale(
        scale: _scaleAnimation.value,
        child: Container(
          width: tooltipWidth,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    step.icon,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      step.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (!isSingleFeature)
                    Text(
                      '${_currentStepIndex + 1}/${widget.steps.length}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                step.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (!isSingleFeature && _currentStepIndex > 0)
                    TextButton(
                      onPressed: _previousStep,
                      child: const Text('Précédent'),
                    )
                  else
                    const SizedBox(),
                  Row(
                    children: [
                      if (!isSingleFeature)
                        TextButton(
                          onPressed: _skipTour,
                          child: const Text('Ignorer'),
                        ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: isSingleFeature ? _removeOverlay : _nextStep,
                        child: Text(
                          isSingleFeature
                              ? 'Compris'
                              : (_currentStepIndex == widget.steps.length - 1
                                  ? 'Terminer'
                                  : 'Suivant'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _nextStep() {
    HapticFeedback.lightImpact();
    _animationController.reverse().then((_) {
      setState(() {
        _currentStepIndex++;
      });
      _showCurrentStep();
    });
  }

  void _previousStep() {
    HapticFeedback.lightImpact();
    _animationController.reverse().then((_) {
      setState(() {
        _currentStepIndex--;
      });
      _showCurrentStep();
    });
  }

  void _skipTour() {
    HapticFeedback.mediumImpact();
    _completeTour();
  }

  void _completeTour() {
    _animationController.reverse().then((_) {
      _removeOverlay();
      setState(() {
        _isActive = false;
        _currentStepIndex = 0;
      });
      widget.onComplete?.call();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    _removeOverlay();
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }
}

/// Represents a single step in the feature discovery tour
class FeatureDiscoveryStep {
  final String key;
  final GlobalKey targetKey;
  final String title;
  final String description;
  final IconData icon;
  final bool autoAdvance;
  final Duration? duration;

  const FeatureDiscoveryStep({
    required this.key,
    required this.targetKey,
    required this.title,
    required this.description,
    required this.icon,
    this.autoAdvance = false,
    this.duration,
  });
}

/// Custom clipper to create a hole in the overlay
class _HoleClipper extends CustomClipper<Path> {
  final Rect holeRect;

  _HoleClipper({required this.holeRect});

  @override
  Path getClip(Size size) {
    final path = Path();
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    path.addRRect(RRect.fromRectAndRadius(holeRect, const Radius.circular(8)));
    path.fillType = PathFillType.evenOdd;
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => true;
}
