import 'package:flutter/material.dart';
import '../../models/is/is_calculation_result.dart';
import '../../models/is/is_input_data.dart';
import '../../utils/calculation_utils.dart';

/// Comprehensive results display widget for Step 3 of the IS calculator wizard
/// 
/// This widget displays calculation results with a gradient background and card layout
/// similar to the TVA invoice summary, with expandable sections for detailed breakdown.
class ResultsSummaryCard extends StatelessWidget {
  /// Calculation results to display
  final IsCalculationResult result;
  
  /// Input data for context
  final IsInputData inputData;
  
  /// Whether to show detailed calculation breakdown
  final bool showBreakdown;
  
  /// Whether to show fiscal analysis section
  final bool showAnalysis;
  
  /// Optional export callback
  final VoidCallback? onExport;
  
  /// Custom padding
  final EdgeInsets? padding;

  const ResultsSummaryCard({
    super.key,
    required this.result,
    required this.inputData,
    this.showBreakdown = true,
    this.showAnalysis = true,
    this.onExport,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    if (_isEmpty) {
      return _buildEmptyState(context);
    }

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Card(
        elevation: 4,
        surfaceTintColor: colorScheme.surfaceTint,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section with gradient
            _buildHeader(context),
            
            // Main Results Section
            _buildMainResults(context),
            
            // Regime Information Section
            _buildRegimeInformation(context),
            
            // Calculation Breakdown Section
            if (showBreakdown) _buildCalculationBreakdown(context),
            
            // Fiscal Analysis Section
            if (showAnalysis) _buildFiscalAnalysis(context),
            
            // Export Actions Section
            if (onExport != null) _buildExportActions(context),
          ],
        ),
      ),
    );
  }

  /// Check if the result is empty or invalid
  bool get _isEmpty {
    return result.regimeName.isEmpty || 
           (result.isAmount == 0 && result.cmAmount == 0);
  }

  /// Build empty state when no results
  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Card(
        elevation: 2,
        surfaceTintColor: colorScheme.surfaceTint,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.calculate_outlined,
                size: 48,
                color: colorScheme.outline,
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun résultat',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Complétez les étapes précédentes pour voir les résultats du calcul IS',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header with gradient background
  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primary.withValues(alpha: 0.8),
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.assessment,
            color: colorScheme.onPrimary,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Résultats du calcul IS',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Régime: ${result.regimeName}',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              result.formattedEffectiveRate,
              style: textTheme.labelMedium?.copyWith(
                color: colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build main results display
  Widget _buildMainResults(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Résultats Principaux',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          
          // Taxable Result
          _buildResultRow(
            context,
            'Résultat Imposable:',
            result.formattedTaxableResult,
            colorScheme.outline,
            isSubtle: true,
          ),
          
          const SizedBox(height: 8),
          Divider(color: colorScheme.outlineVariant),
          const SizedBox(height: 8),
          
          // IS Amount
          _buildResultRow(
            context,
            'Impôt sur les Sociétés (IS):',
            result.formattedIsAmount,
            colorScheme.primary,
            isLarge: false,
          ),
          
          const SizedBox(height: 8),
          
          // CM Amount
          _buildResultRow(
            context,
            'Cotisation Minimale (CM):',
            result.formattedCmAmount,
            colorScheme.secondary,
            isLarge: false,
          ),
          
          const SizedBox(height: 8),
          Divider(color: colorScheme.outlineVariant),
          const SizedBox(height: 8),
          
          // Total Payable
          _buildResultRow(
            context,
            'Total à Payer:',
            result.formattedTotalPayable,
            colorScheme.tertiary,
            isLarge: true,
          ),
        ],
      ),
    );
  }

  /// Build regime information section
  Widget _buildRegimeInformation(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primaryContainer,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Informations du Régime',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            context,
            'Régime appliqué:',
            result.regimeName,
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            context,
            'Type de taux:',
            result.rateType == 'progressive' ? 'Progressif' : 'Fixe',
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            context,
            'Taux appliqué:',
            result.formattedAppliedRate,
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            context,
            'Taux effectif:',
            result.formattedEffectiveRate,
          ),
        ],
      ),
    );
  }

  /// Build calculation breakdown section
  Widget _buildCalculationBreakdown(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ExpansionTile(
      title: Text(
        'Détail du Calcul',
        style: textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: colorScheme.onSurface,
        ),
      ),
      leading: Icon(
        Icons.calculate,
        color: colorScheme.primary,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Base calculation
              _buildCalculationStep(
                context,
                '1. Résultat comptable',
                CalculationUtils.formatMonetary(inputData.accountingResult),
              ),
              
              // Reintegrations
              if (inputData.totalReintegrations > 0) ...[
                const SizedBox(height: 8),
                _buildCalculationStep(
                  context,
                  '2. + Réintégrations',
                  CalculationUtils.formatMonetary(inputData.totalReintegrations),
                  isPositive: true,
                ),
              ],
              
              // Deductions
              if (inputData.totalDeductions > 0) ...[
                const SizedBox(height: 8),
                _buildCalculationStep(
                  context,
                  '3. - Déductions',
                  CalculationUtils.formatMonetary(inputData.totalDeductions),
                  isNegative: true,
                ),
              ],
              
              const SizedBox(height: 8),
              Divider(color: colorScheme.outlineVariant),
              const SizedBox(height: 8),
              
              // Taxable result
              _buildCalculationStep(
                context,
                'Résultat imposable',
                result.formattedTaxableResult,
                isFinal: true,
              ),
              
              const SizedBox(height: 16),
              
              // Tax calculation
              if (result.appliedBrackets.isNotEmpty) 
                _buildProgressiveTaxBreakdown(context)
              else
                _buildFixedTaxCalculation(context),
            ],
          ),
        ),
      ],
    );
  }

  /// Build fiscal analysis section
  Widget _buildFiscalAnalysis(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (result.fiscalAnalysis.isEmpty && result.recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return ExpansionTile(
      title: Text(
        'Analyse Fiscale',
        style: textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: colorScheme.onSurface,
        ),
      ),
      leading: Icon(
        Icons.analytics_outlined,
        color: colorScheme.secondary,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Fiscal analysis text
              if (result.fiscalAnalysis.isNotEmpty) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.secondaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.secondaryContainer,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    result.fiscalAnalysis,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSecondaryContainer,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
              
              // Recommendations
              if (result.recommendations.isNotEmpty) ...[
                Text(
                  'Recommandations',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                ...result.recommendations.map((recommendation) => 
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: colorScheme.tertiary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            recommendation,
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Build export actions section
  Widget _buildExportActions(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onExport,
              icon: const Icon(Icons.share),
              label: const Text('Partager'),
              style: OutlinedButton.styleFrom(
                foregroundColor: colorScheme.primary,
                side: BorderSide(color: colorScheme.outline),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: FilledButton.icon(
              onPressed: onExport,
              icon: const Icon(Icons.download),
              label: const Text('Exporter'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a result row with proper styling
  Widget _buildResultRow(
    BuildContext context,
    String label,
    String amount,
    Color color, {
    bool isLarge = false,
    bool isSubtle = false,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: (isLarge ? textTheme.titleMedium : textTheme.bodyLarge)?.copyWith(
              color: isSubtle 
                ? Theme.of(context).colorScheme.onSurfaceVariant
                : Theme.of(context).colorScheme.onSurface,
              fontWeight: isLarge ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Text(
          amount,
          style: (isLarge ? textTheme.titleLarge : textTheme.titleMedium)?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// Build an info row for regime information
  Widget _buildInfoRow(BuildContext context, String label, String value) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          value,
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// Build a calculation step
  Widget _buildCalculationStep(
    BuildContext context,
    String label,
    String amount, {
    bool isPositive = false,
    bool isNegative = false,
    bool isFinal = false,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    Color getColor() {
      if (isFinal) return colorScheme.primary;
      if (isPositive) return colorScheme.secondary;
      if (isNegative) return colorScheme.error;
      return colorScheme.onSurface;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: getColor(),
              fontWeight: isFinal ? FontWeight.w600 : FontWeight.normal,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          amount,
          style: textTheme.bodyMedium?.copyWith(
            color: getColor(),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// Build progressive tax breakdown
  Widget _buildProgressiveTaxBreakdown(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Calcul progressif:',
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        ...result.appliedBrackets.map((bracket) {
          // This would need to be implemented based on the TaxBracket model
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tranche ${bracket.formattedRange}',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  bracket.formattedRate,
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// Build fixed tax calculation
  Widget _buildFixedTaxCalculation(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Calcul IS:',
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        _buildCalculationStep(
          context,
          '${result.formattedTaxableResult} × ${result.formattedAppliedRate}',
          result.formattedIsAmount,
          isFinal: true,
        ),
        const SizedBox(height: 8),
        _buildCalculationStep(
          context,
          'Cotisation Minimale',
          result.formattedCmAmount,
        ),
      ],
    );
  }
}