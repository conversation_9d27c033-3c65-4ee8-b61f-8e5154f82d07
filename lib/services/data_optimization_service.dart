import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:crypto/crypto.dart';

import '../models/hive/cache_entry.dart';
import 'offline_cache_service.dart';

/// Comprehensive data optimization service for the Moroccan Accounting app
/// Handles intelligent caching, data compression, and background synchronization
class DataOptimizationService extends ChangeNotifier {
  static const String _optimizationBoxName = 'data_optimization';
  static const String _compressionBoxName = 'compressed_data';
  static const String _migrationBoxName = 'data_migration';
  
  Box<Map>? _optimizationBox;
  Box<Uint8List>? _compressionBox;
  Box<String>? _migrationBox;
  
  // Data management
  final Map<String, dynamic> _dataCache = {};
  final Map<String, Timer> _expirationTimers = {};
  final Map<String, Completer<dynamic>> _loadingOperations = {};
  
  // Compression settings
  static const int _compressionThreshold = 1024; // 1KB
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Background sync
  Timer? _backgroundSyncTimer;
  final Set<String> _pendingSyncKeys = {};
  
  // Data integrity
  final Map<String, String> _dataChecksums = {};
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  /// Initialize the data optimization service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeHiveBoxes();
      await _loadOptimizationSettings();
      await _performDataMigration();
      _setupBackgroundSync();
      _setupDataIntegrityChecks();
      _isInitialized = true;
      
      debugPrint('DataOptimizationService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing DataOptimizationService: $e');
      rethrow;
    }
  }
  
  /// Initialize Hive boxes for data optimization
  Future<void> _initializeHiveBoxes() async {
    try {
      _optimizationBox = await Hive.openBox<Map>(_optimizationBoxName);
      _compressionBox = await Hive.openBox<Uint8List>(_compressionBoxName);
      _migrationBox = await Hive.openBox<String>(_migrationBoxName);
    } catch (e) {
      debugPrint('Error opening data optimization Hive boxes: $e');
      rethrow;
    }
  }
  
  /// Load optimization settings
  Future<void> _loadOptimizationSettings() async {
    try {
      final settings = _optimizationBox?.get('settings');
      if (settings != null) {
        // Load any saved optimization preferences
        debugPrint('Loaded optimization settings');
      }
    } catch (e) {
      debugPrint('Error loading optimization settings: $e');
    }
  }
  
  /// Perform data migration if needed
  Future<void> _performDataMigration() async {
    try {
      final currentVersion = _migrationBox?.get('version') ?? '1.0.0';
      const targetVersion = '1.1.0';
      
      if (currentVersion != targetVersion) {
        await _migrateData(currentVersion, targetVersion);
        await _migrationBox?.put('version', targetVersion);
        debugPrint('Data migration completed: $currentVersion -> $targetVersion');
      }
    } catch (e) {
      debugPrint('Error during data migration: $e');
    }
  }
  
  /// Migrate data between versions
  Future<void> _migrateData(String fromVersion, String toVersion) async {
    // Implement version-specific migration logic
    switch (fromVersion) {
      case '1.0.0':
        await _migrateFrom1_0_0();
        break;
      default:
        debugPrint('No migration needed for version $fromVersion');
    }
  }
  
  /// Migration from version 1.0.0
  Future<void> _migrateFrom1_0_0() async {
    try {
      // Example migration: compress existing large data entries
      final entries = _optimizationBox?.values.toList() ?? [];
      for (final entry in entries) {
        if (entry is Map && entry.containsKey('large_data')) {
          final data = entry['large_data'];
          if (data is String && data.length > _compressionThreshold) {
            final compressed = await _compressData(data);
            entry['large_data'] = compressed;
          }
        }
      }
      debugPrint('Migration from 1.0.0 completed');
    } catch (e) {
      debugPrint('Error migrating from 1.0.0: $e');
    }
  }
  
  /// Setup background synchronization
  void _setupBackgroundSync() {
    _backgroundSyncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _performBackgroundSync();
    });
  }
  
  /// Setup data integrity checks
  void _setupDataIntegrityChecks() {
    Timer.periodic(const Duration(hours: 1), (_) {
      _performIntegrityCheck();
    });
  }
  
  /// Store data with intelligent caching and compression
  Future<void> storeData(
    String key,
    dynamic data, {
    Duration? expiration,
    bool compress = true,
    bool syncInBackground = false,
  }) async {
    try {
      final serializedData = jsonEncode(data);
      final dataSize = serializedData.length;
      
      // Generate checksum for integrity
      final checksum = _generateChecksum(serializedData);
      _dataChecksums[key] = checksum;
      
      // Decide whether to compress
      final shouldCompress = compress && dataSize > _compressionThreshold;
      
      if (shouldCompress) {
        final compressedData = await _compressData(serializedData);
        await _compressionBox?.put(key, compressedData);
        
        // Store metadata
        await _optimizationBox?.put(key, {
          'compressed': true,
          'original_size': dataSize,
          'compressed_size': compressedData.length,
          'checksum': checksum,
          'timestamp': DateTime.now().toIso8601String(),
          'expiration': expiration?.inMilliseconds,
        });
      } else {
        // Store uncompressed
        _dataCache[key] = data;
        
        await _optimizationBox?.put(key, {
          'compressed': false,
          'size': dataSize,
          'checksum': checksum,
          'timestamp': DateTime.now().toIso8601String(),
          'expiration': expiration?.inMilliseconds,
        });
      }
      
      // Set expiration timer
      if (expiration != null) {
        _expirationTimers[key]?.cancel();
        _expirationTimers[key] = Timer(expiration, () {
          _expireData(key);
        });
      }
      
      // Mark for background sync if requested
      if (syncInBackground) {
        _pendingSyncKeys.add(key);
      }
      
      debugPrint('Data stored: $key (${shouldCompress ? 'compressed' : 'uncompressed'})');
    } catch (e) {
      debugPrint('Error storing data for key $key: $e');
      rethrow;
    }
  }
  
  /// Retrieve data with automatic decompression
  Future<T?> retrieveData<T>(String key) async {
    try {
      // Check memory cache first
      if (_dataCache.containsKey(key)) {
        return _dataCache[key] as T?;
      }
      
      // Check metadata
      final metadata = _optimizationBox?.get(key);
      if (metadata == null) return null;
      
      // Check if expired
      final expiration = metadata['expiration'] as int?;
      if (expiration != null) {
        final expirationTime = DateTime.fromMillisecondsSinceEpoch(expiration);
        if (DateTime.now().isAfter(expirationTime)) {
          await _expireData(key);
          return null;
        }
      }
      
      final isCompressed = metadata['compressed'] as bool? ?? false;
      
      if (isCompressed) {
        // Retrieve and decompress
        final compressedData = _compressionBox?.get(key);
        if (compressedData == null) return null;
        
        final decompressedData = await _decompressData(compressedData);
        final data = jsonDecode(decompressedData);
        
        // Verify integrity
        final expectedChecksum = metadata['checksum'] as String?;
        if (expectedChecksum != null) {
          final actualChecksum = _generateChecksum(decompressedData);
          if (actualChecksum != expectedChecksum) {
            debugPrint('Data integrity check failed for key: $key');
            await _expireData(key);
            return null;
          }
        }
        
        // Cache in memory for faster access
        _dataCache[key] = data;
        return data as T?;
      } else {
        // Data should be in memory cache or needs to be loaded
        return _dataCache[key] as T?;
      }
    } catch (e) {
      debugPrint('Error retrieving data for key $key: $e');
      return null;
    }
  }
  
  /// Compress data using gzip
  Future<Uint8List> _compressData(String data) async {
    return await compute(_compressDataIsolate, data);
  }
  
  /// Decompress data using gzip
  Future<String> _decompressData(Uint8List compressedData) async {
    return await compute(_decompressDataIsolate, compressedData);
  }
  
  /// Generate checksum for data integrity
  String _generateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// Expire data and clean up
  Future<void> _expireData(String key) async {
    try {
      _dataCache.remove(key);
      await _optimizationBox?.delete(key);
      await _compressionBox?.delete(key);
      _expirationTimers[key]?.cancel();
      _expirationTimers.remove(key);
      _dataChecksums.remove(key);
      _pendingSyncKeys.remove(key);
      
      debugPrint('Data expired and cleaned up: $key');
    } catch (e) {
      debugPrint('Error expiring data for key $key: $e');
    }
  }
  
  /// Perform background synchronization
  Future<void> _performBackgroundSync() async {
    if (_pendingSyncKeys.isEmpty) return;
    
    try {
      final keysToSync = List<String>.from(_pendingSyncKeys);
      _pendingSyncKeys.clear();
      
      for (final key in keysToSync) {
        // Implement actual sync logic here
        // This could involve uploading to cloud storage, etc.
        debugPrint('Background sync for key: $key');
      }
      
      debugPrint('Background sync completed for ${keysToSync.length} items');
    } catch (e) {
      debugPrint('Error during background sync: $e');
    }
  }
  
  /// Perform data integrity check
  Future<void> _performIntegrityCheck() async {
    try {
      int corruptedCount = 0;
      final keys = _optimizationBox?.keys.toList() ?? [];
      
      for (final key in keys) {
        final metadata = _optimizationBox?.get(key);
        if (metadata == null) continue;
        
        final expectedChecksum = metadata['checksum'] as String?;
        if (expectedChecksum == null) continue;
        
        final isCompressed = metadata['compressed'] as bool? ?? false;
        
        if (isCompressed) {
          final compressedData = _compressionBox?.get(key);
          if (compressedData != null) {
            try {
              final decompressedData = await _decompressData(compressedData);
              final actualChecksum = _generateChecksum(decompressedData);
              
              if (actualChecksum != expectedChecksum) {
                await _expireData(key);
                corruptedCount++;
              }
            } catch (e) {
              await _expireData(key);
              corruptedCount++;
            }
          }
        }
      }
      
      if (corruptedCount > 0) {
        debugPrint('Integrity check completed: $corruptedCount corrupted entries removed');
      }
    } catch (e) {
      debugPrint('Error during integrity check: $e');
    }
  }
  
  /// Get optimization statistics
  Map<String, dynamic> getOptimizationStats() {
    final totalEntries = _optimizationBox?.length ?? 0;
    final compressedEntries = _compressionBox?.length ?? 0;
    final memoryEntries = _dataCache.length;
    
    int totalOriginalSize = 0;
    int totalCompressedSize = 0;
    
    for (final metadata in _optimizationBox?.values.cast<Map>() ?? <Map>[]) {
      final originalSize = metadata['original_size'] as int? ?? 0;
      final compressedSize = metadata['compressed_size'] as int? ?? 0;

      totalOriginalSize += originalSize;
      totalCompressedSize += compressedSize;
    }
    
    final compressionRatio = totalOriginalSize > 0 
        ? (totalCompressedSize / totalOriginalSize) * 100 
        : 0.0;
    
    return {
      'total_entries': totalEntries,
      'compressed_entries': compressedEntries,
      'memory_entries': memoryEntries,
      'total_original_size_bytes': totalOriginalSize,
      'total_compressed_size_bytes': totalCompressedSize,
      'compression_ratio_percent': compressionRatio.toStringAsFixed(2),
      'space_saved_bytes': totalOriginalSize - totalCompressedSize,
      'pending_sync_items': _pendingSyncKeys.length,
      'active_timers': _expirationTimers.length,
    };
  }
  
  /// Clear all cached data
  Future<void> clearAllData() async {
    try {
      _dataCache.clear();
      await _optimizationBox?.clear();
      await _compressionBox?.clear();
      
      for (final timer in _expirationTimers.values) {
        timer.cancel();
      }
      _expirationTimers.clear();
      _dataChecksums.clear();
      _pendingSyncKeys.clear();
      
      debugPrint('All cached data cleared');
    } catch (e) {
      debugPrint('Error clearing all data: $e');
    }
  }
  
  /// Dispose of the service
  @override
  void dispose() {
    _backgroundSyncTimer?.cancel();
    
    for (final timer in _expirationTimers.values) {
      timer.cancel();
    }
    _expirationTimers.clear();
    
    super.dispose();
  }
}

/// Isolate function for data compression
Uint8List _compressDataIsolate(String data) {
  final bytes = utf8.encode(data);
  return Uint8List.fromList(gzip.encode(bytes));
}

/// Isolate function for data decompression
String _decompressDataIsolate(Uint8List compressedData) {
  final decompressed = gzip.decode(compressedData);
  return utf8.decode(decompressed);
}
