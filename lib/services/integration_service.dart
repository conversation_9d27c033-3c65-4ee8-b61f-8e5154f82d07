import 'dart:async';
import 'package:flutter/foundation.dart';

import 'guide_progress_service.dart';
import 'quiz_progress_service.dart';

/// Integration service to better connect existing features
/// Implements cross-feature data sharing, unified search, and seamless navigation
class IntegrationService extends ChangeNotifier {
  final GuideProgressService _guideProgressService;
  final QuizProgressService _quizProgressService;
  
  // Cross-feature data sharing
  final Map<String, dynamic> _sharedData = {};
  final Map<String, List<String>> _featureConnections = {};
  
  // Unified search index
  final Map<String, SearchableItem> _searchIndex = {};
  final Map<String, List<String>> _searchCategories = {};
  
  // Navigation flow tracking
  final List<NavigationEvent> _navigationHistory = [];
  final Map<String, String> _featureRelationships = {};
  
  // Progress synchronization
  final Map<String, ProgressData> _unifiedProgress = {};
  Timer? _syncTimer;
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  IntegrationService({
    required GuideProgressService guideProgressService,
    required QuizProgressService quizProgressService,
  }) : _guideProgressService = guideProgressService,
       _quizProgressService = quizProgressService;
  
  /// Initialize the integration service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _buildSearchIndex();
      await _establishFeatureConnections();
      await _synchronizeProgress();
      _setupPeriodicSync();
      _isInitialized = true;
      
      debugPrint('IntegrationService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing IntegrationService: $e');
      rethrow;
    }
  }
  
  /// Build unified search index across all content types
  Future<void> _buildSearchIndex() async {
    try {
      // Index guides
      await _indexGuides();
      
      // Index quizzes
      await _indexQuizzes();
      
      // Index calculators
      await _indexCalculators();
      
      // Index other features
      await _indexOtherFeatures();
      
      debugPrint('Search index built with ${_searchIndex.length} items');
    } catch (e) {
      debugPrint('Error building search index: $e');
    }
  }
  
  /// Index guide content for search
  Future<void> _indexGuides() async {
    final guides = [
      'ir', 'is', 'comptabilite_generale', 'droits_enregistrement'
    ];
    
    for (final guideId in guides) {
      _searchIndex['guide_$guideId'] = SearchableItem(
        id: 'guide_$guideId',
        title: _getGuideTitle(guideId),
        content: _getGuideContent(guideId),
        category: 'guides',
        type: ContentType.guide,
        keywords: _getGuideKeywords(guideId),
        route: '/guide/$guideId',
      );
      
      _searchCategories.putIfAbsent('guides', () => []).add('guide_$guideId');
    }
  }
  
  /// Index quiz content for search
  Future<void> _indexQuizzes() async {
    final quizCategories = [
      'comptabilite', 'fiscalite', 'audit', 'finance'
    ];
    
    for (final category in quizCategories) {
      _searchIndex['quiz_$category'] = SearchableItem(
        id: 'quiz_$category',
        title: 'Quiz $category',
        content: 'Questions et exercices sur $category',
        category: 'quizzes',
        type: ContentType.quiz,
        keywords: [category, 'quiz', 'exercices', 'questions'],
        route: '/quiz?category=$category',
      );
      
      _searchCategories.putIfAbsent('quizzes', () => []).add('quiz_$category');
    }
  }
  
  /// Index calculator tools for search
  Future<void> _indexCalculators() async {
    final calculators = [
      {
        'id': 'depreciation',
        'title': 'Calculateur d\'Amortissement',
        'content': 'Calcul des amortissements linéaires et dégressifs',
        'keywords': ['amortissement', 'depreciation', 'calcul', 'immobilisation'],
      },
      {
        'id': 'financial_ratios',
        'title': 'Ratios Financiers',
        'content': 'Calcul des ratios de liquidité, rentabilité et solvabilité',
        'keywords': ['ratios', 'financier', 'liquidité', 'rentabilité'],
      },
      {
        'id': 'tax_optimization',
        'title': 'Optimisation Fiscale',
        'content': 'Outils d\'optimisation et de planification fiscale',
        'keywords': ['fiscalité', 'optimisation', 'impôt', 'planification'],
      },
    ];
    
    for (final calc in calculators) {
      _searchIndex['calculator_${calc['id']}'] = SearchableItem(
        id: 'calculator_${calc['id']}',
        title: calc['title'] as String,
        content: calc['content'] as String,
        category: 'calculators',
        type: ContentType.calculator,
        keywords: calc['keywords'] as List<String>,
        route: '/calculator/${calc['id']}',
      );
      
      _searchCategories.putIfAbsent('calculators', () => []).add('calculator_${calc['id']}');
    }
  }
  
  /// Index other features for search
  Future<void> _indexOtherFeatures() async {
    final features = [
      {
        'id': 'spaced_repetition',
        'title': 'Répétition Espacée',
        'content': 'Système d\'apprentissage par répétition espacée',
        'keywords': ['répétition', 'mémorisation', 'apprentissage', 'révision'],
        'route': '/spaced_repetition_review',
      },
      {
        'id': 'analytics',
        'title': 'Analytics de Performance',
        'content': 'Analyse détaillée de vos performances d\'apprentissage',
        'keywords': ['analytics', 'performance', 'statistiques', 'progrès'],
        'route': '/performance_analytics',
      },
      {
        'id': 'notes',
        'title': 'Notes Collaboratives',
        'content': 'Création et partage de notes personnalisées',
        'keywords': ['notes', 'collaboration', 'partage', 'personnel'],
        'route': '/personal_notes',
      },
    ];
    
    for (final feature in features) {
      _searchIndex['feature_${feature['id']}'] = SearchableItem(
        id: 'feature_${feature['id']}',
        title: feature['title'] as String,
        content: feature['content'] as String,
        category: 'features',
        type: ContentType.feature,
        keywords: feature['keywords'] as List<String>,
        route: feature['route'] as String,
      );
      
      _searchCategories.putIfAbsent('features', () => []).add('feature_${feature['id']}');
    }
  }
  
  /// Establish connections between features
  Future<void> _establishFeatureConnections() async {
    // Connect related guides and quizzes
    _featureConnections['guide_ir'] = ['quiz_fiscalite', 'calculator_tax_optimization'];
    _featureConnections['guide_is'] = ['quiz_fiscalite', 'calculator_financial_ratios'];
    _featureConnections['guide_comptabilite_generale'] = ['quiz_comptabilite', 'calculator_depreciation'];
    
    // Connect calculators with related content
    _featureConnections['calculator_depreciation'] = ['guide_comptabilite_generale', 'quiz_comptabilite'];
    _featureConnections['calculator_financial_ratios'] = ['guide_is', 'quiz_finance'];
    
    // Connect learning features
    _featureConnections['feature_spaced_repetition'] = ['quiz_comptabilite', 'quiz_fiscalite'];
    _featureConnections['feature_analytics'] = ['quiz_comptabilite', 'quiz_fiscalite', 'guide_ir'];
    
    debugPrint('Feature connections established');
  }
  
  /// Synchronize progress across features
  Future<void> _synchronizeProgress() async {
    try {
      // Sync guide progress
      await _syncGuideProgress();
      
      // Sync quiz progress
      await _syncQuizProgress();
      
      // Calculate unified progress metrics
      _calculateUnifiedProgress();
      
      debugPrint('Progress synchronization completed');
    } catch (e) {
      debugPrint('Error synchronizing progress: $e');
    }
  }
  
  /// Sync guide progress data
  Future<void> _syncGuideProgress() async {
    // Implementation would depend on actual GuideProgressService API
    // This is a placeholder for the integration
  }
  
  /// Sync quiz progress data
  Future<void> _syncQuizProgress() async {
    // Implementation would depend on actual QuizProgressService API
    // This is a placeholder for the integration
  }
  
  /// Calculate unified progress metrics
  void _calculateUnifiedProgress() {
    // Combine progress from different features
    _unifiedProgress['overall'] = ProgressData(
      completionPercentage: 0.65, // Calculated from all features
      timeSpent: const Duration(hours: 25),
      itemsCompleted: 45,
      totalItems: 70,
      lastActivity: DateTime.now().subtract(const Duration(hours: 2)),
    );
    
    _unifiedProgress['learning'] = ProgressData(
      completionPercentage: 0.70,
      timeSpent: const Duration(hours: 15),
      itemsCompleted: 28,
      totalItems: 40,
      lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
    );
  }
  
  /// Setup periodic synchronization
  void _setupPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _synchronizeProgress();
    });
  }
  
  /// Perform unified search across all content
  List<SearchResult> search(String query, {
    List<String>? categories,
    int limit = 20,
  }) {
    if (query.isEmpty) return [];
    
    final results = <SearchResult>[];
    final queryLower = query.toLowerCase();
    
    for (final item in _searchIndex.values) {
      // Filter by category if specified
      if (categories != null && !categories.contains(item.category)) {
        continue;
      }
      
      final score = _calculateSearchScore(item, queryLower);
      if (score > 0) {
        results.add(SearchResult(
          item: item,
          score: score,
          matchedKeywords: _getMatchedKeywords(item, queryLower),
        ));
      }
    }
    
    // Sort by score and limit results
    results.sort((a, b) => b.score.compareTo(a.score));
    return results.take(limit).toList();
  }
  
  /// Calculate search relevance score
  double _calculateSearchScore(SearchableItem item, String query) {
    double score = 0.0;
    
    // Title match (highest weight)
    if (item.title.toLowerCase().contains(query)) {
      score += 10.0;
    }
    
    // Content match
    if (item.content.toLowerCase().contains(query)) {
      score += 5.0;
    }
    
    // Keyword matches
    for (final keyword in item.keywords) {
      if (keyword.toLowerCase().contains(query)) {
        score += 3.0;
      }
    }
    
    // Partial matches
    final queryWords = query.split(' ');
    for (final word in queryWords) {
      if (word.length > 2) {
        if (item.title.toLowerCase().contains(word)) score += 2.0;
        if (item.content.toLowerCase().contains(word)) score += 1.0;
      }
    }
    
    return score;
  }
  
  /// Get matched keywords for highlighting
  List<String> _getMatchedKeywords(SearchableItem item, String query) {
    final matched = <String>[];
    
    for (final keyword in item.keywords) {
      if (keyword.toLowerCase().contains(query)) {
        matched.add(keyword);
      }
    }
    
    return matched;
  }
  
  /// Get related content for a given item
  List<SearchableItem> getRelatedContent(String itemId, {int limit = 5}) {
    final related = <SearchableItem>[];
    
    // Get directly connected items
    final connections = _featureConnections[itemId] ?? [];
    for (final connectionId in connections) {
      final item = _searchIndex[connectionId];
      if (item != null) {
        related.add(item);
      }
    }
    
    // If we need more items, find similar content
    if (related.length < limit) {
      final currentItem = _searchIndex[itemId];
      if (currentItem != null) {
        final similar = _findSimilarContent(currentItem, limit - related.length);
        related.addAll(similar);
      }
    }
    
    return related.take(limit).toList();
  }
  
  /// Find similar content based on keywords and category
  List<SearchableItem> _findSimilarContent(SearchableItem item, int limit) {
    final similar = <SearchableItem>[];
    
    for (final candidate in _searchIndex.values) {
      if (candidate.id == item.id) continue;
      
      // Same category gets priority
      if (candidate.category == item.category) {
        similar.add(candidate);
        continue;
      }
      
      // Check keyword overlap
      final commonKeywords = item.keywords
          .where((keyword) => candidate.keywords.contains(keyword))
          .length;
      
      if (commonKeywords > 0) {
        similar.add(candidate);
      }
    }
    
    return similar.take(limit).toList();
  }
  
  /// Track navigation between features
  void trackNavigation(String fromFeature, String toFeature, String context) {
    final event = NavigationEvent(
      fromFeature: fromFeature,
      toFeature: toFeature,
      context: context,
      timestamp: DateTime.now(),
    );
    
    _navigationHistory.add(event);
    
    // Keep history manageable
    if (_navigationHistory.length > 1000) {
      _navigationHistory.removeRange(0, _navigationHistory.length - 1000);
    }
    
    // Update feature relationships
    _featureRelationships[fromFeature] = toFeature;
    
    notifyListeners();
  }
  
  /// Get unified progress data
  ProgressData? getUnifiedProgress([String? category]) {
    return _unifiedProgress[category ?? 'overall'];
  }
  
  /// Share data between features
  void shareData(String key, dynamic data, {List<String>? targetFeatures}) {
    _sharedData[key] = data;
    
    // Notify specific features if specified
    if (targetFeatures != null) {
      for (final feature in targetFeatures) {
        // Implementation would notify specific feature services
        debugPrint('Sharing data with feature: $feature');
      }
    }
    
    notifyListeners();
  }
  
  /// Get shared data
  T? getSharedData<T>(String key) {
    return _sharedData[key] as T?;
  }
  
  /// Get search categories
  List<String> getSearchCategories() {
    return _searchCategories.keys.toList();
  }
  
  /// Get navigation insights
  Map<String, dynamic> getNavigationInsights() {
    final insights = <String, dynamic>{};
    
    // Most common navigation paths
    final pathCounts = <String, int>{};
    for (final event in _navigationHistory) {
      final path = '${event.fromFeature} -> ${event.toFeature}';
      pathCounts[path] = (pathCounts[path] ?? 0) + 1;
    }
    
    insights['common_paths'] = pathCounts.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    
    insights['total_navigations'] = _navigationHistory.length;
    insights['unique_features'] = _featureRelationships.keys.length;
    
    return insights;
  }
  
  // Helper methods for guide data
  String _getGuideTitle(String guideId) {
    switch (guideId) {
      case 'ir': return 'Impôt sur le Revenu';
      case 'is': return 'Impôt sur les Sociétés';
      case 'comptabilite_generale': return 'Comptabilité Générale';
      case 'droits_enregistrement': return 'Droits d\'Enregistrement';
      default: return guideId;
    }
  }
  
  String _getGuideContent(String guideId) {
    return 'Guide complet sur ${_getGuideTitle(guideId)}';
  }
  
  List<String> _getGuideKeywords(String guideId) {
    switch (guideId) {
      case 'ir': return ['impôt', 'revenu', 'IR', 'fiscal', 'déclaration'];
      case 'is': return ['impôt', 'sociétés', 'IS', 'entreprise', 'bénéfice'];
      case 'comptabilite_generale': return ['comptabilité', 'bilan', 'compte', 'écriture'];
      case 'droits_enregistrement': return ['droits', 'enregistrement', 'acte', 'notaire'];
      default: return [guideId];
    }
  }
  
  /// Dispose of the service
  @override
  void dispose() {
    _syncTimer?.cancel();
    super.dispose();
  }
}

// Data classes
class SearchableItem {
  final String id;
  final String title;
  final String content;
  final String category;
  final ContentType type;
  final List<String> keywords;
  final String route;

  SearchableItem({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.type,
    required this.keywords,
    required this.route,
  });
}

class SearchResult {
  final SearchableItem item;
  final double score;
  final List<String> matchedKeywords;

  SearchResult({
    required this.item,
    required this.score,
    required this.matchedKeywords,
  });
}

class NavigationEvent {
  final String fromFeature;
  final String toFeature;
  final String context;
  final DateTime timestamp;

  NavigationEvent({
    required this.fromFeature,
    required this.toFeature,
    required this.context,
    required this.timestamp,
  });
}

class ProgressData {
  final double completionPercentage;
  final Duration timeSpent;
  final int itemsCompleted;
  final int totalItems;
  final DateTime lastActivity;

  ProgressData({
    required this.completionPercentage,
    required this.timeSpent,
    required this.itemsCompleted,
    required this.totalItems,
    required this.lastActivity,
  });
}

enum ContentType { guide, quiz, calculator, feature }
