import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'performance_analytics_service.dart';
import 'social_sharing_service.dart';

/// Comprehensive feature usage analytics service
/// Tracks user engagement with advanced features and provides insights
class FeatureAnalyticsService extends ChangeNotifier {
  static const String _analyticsBoxName = 'feature_analytics';
  static const String _userBehaviorBoxName = 'user_behavior';
  
  Box<Map>? _analyticsBox;
  Box<Map>? _userBehaviorBox;
  
  // Feature usage tracking
  final Map<String, int> _featureUsageCounts = {};
  final Map<String, Duration> _featureUsageDurations = {};
  final Map<String, DateTime> _lastFeatureUsage = {};
  final Map<String, List<DateTime>> _featureUsageHistory = {};
  
  // User engagement metrics
  final Map<String, double> _engagementScores = {};
  final Map<String, List<String>> _userJourneys = {};
  final Map<String, int> _featureDiscoveryMethods = {};
  
  // A/B testing data
  final Map<String, Map<String, dynamic>> _abTestData = {};
  final Map<String, String> _userVariants = {};
  
  // Feature categories for better organization
  static const Map<String, List<String>> _featureCategories = {
    'learning': [
      'spaced_repetition',
      'adaptive_learning',
      'personalized_content',
      'ai_tutor',
      'voice_interaction',
    ],
    'social': [
      'social_sharing',
      'note_collaboration',
      'professional_networking',
      'real_time_collaboration',
    ],
    'analytics': [
      'performance_analytics',
      'advanced_analytics',
      'learning_insights',
      'progress_tracking',
    ],
    'calculators': [
      'enhanced_depreciation',
      'financial_ratios',
      'tax_optimization',
      'advanced_calculations',
    ],
    'gamification': [
      'achievements',
      'daily_goals',
      'learning_streaks',
      'leaderboards',
    ],
    'accessibility': [
      'voice_commands',
      'screen_reader',
      'high_contrast',
      'font_scaling',
    ],
    'advanced': [
      'ar_visualization',
      'blockchain_certification',
      'market_simulation',
      'expert_consultation',
    ],
  };
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  /// Initialize the feature analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeHiveBoxes();
      await _loadAnalyticsData();
      _setupPeriodicReporting();
      _isInitialized = true;
      
      debugPrint('FeatureAnalyticsService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing FeatureAnalyticsService: $e');
      rethrow;
    }
  }
  
  /// Initialize Hive boxes for analytics data
  Future<void> _initializeHiveBoxes() async {
    try {
      _analyticsBox = await Hive.openBox<Map>(_analyticsBoxName);
      _userBehaviorBox = await Hive.openBox<Map>(_userBehaviorBoxName);
    } catch (e) {
      debugPrint('Error opening analytics Hive boxes: $e');
      rethrow;
    }
  }
  
  /// Load existing analytics data
  Future<void> _loadAnalyticsData() async {
    try {
      // Load feature usage data
      final usageData = _analyticsBox?.get('feature_usage');
      if (usageData != null) {
        _featureUsageCounts.addAll(Map<String, int>.from(usageData['counts'] ?? {}));
        
        final durationsData = usageData['durations'] as Map?;
        if (durationsData != null) {
          for (final entry in durationsData.entries) {
            _featureUsageDurations[entry.key] = Duration(milliseconds: entry.value);
          }
        }
        
        final lastUsageData = usageData['last_usage'] as Map?;
        if (lastUsageData != null) {
          for (final entry in lastUsageData.entries) {
            _lastFeatureUsage[entry.key] = DateTime.parse(entry.value);
          }
        }
      }
      
      // Load user behavior data
      final behaviorData = _userBehaviorBox?.get('user_behavior');
      if (behaviorData != null) {
        _engagementScores.addAll(Map<String, double>.from(behaviorData['engagement'] ?? {}));
        
        final journeysData = behaviorData['journeys'] as Map?;
        if (journeysData != null) {
          for (final entry in journeysData.entries) {
            _userJourneys[entry.key] = List<String>.from(entry.value);
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading analytics data: $e');
    }
  }
  
  /// Setup periodic reporting and data cleanup
  void _setupPeriodicReporting() {
    // Save analytics data every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _saveAnalyticsData();
    });
    
    // Generate insights every hour
    Timer.periodic(const Duration(hours: 1), (_) {
      _generateInsights();
    });
    
    // Cleanup old data weekly
    Timer.periodic(const Duration(days: 7), (_) {
      _cleanupOldData();
    });
  }
  
  /// Track feature usage
  void trackFeatureUsage(
    String featureName, {
    Duration? duration,
    Map<String, dynamic>? metadata,
    String? discoveryMethod,
  }) {
    try {
      final now = DateTime.now();
      
      // Update usage counts
      _featureUsageCounts[featureName] = (_featureUsageCounts[featureName] ?? 0) + 1;
      
      // Update duration if provided
      if (duration != null) {
        final currentDuration = _featureUsageDurations[featureName] ?? Duration.zero;
        _featureUsageDurations[featureName] = currentDuration + duration;
      }
      
      // Update last usage time
      _lastFeatureUsage[featureName] = now;
      
      // Add to usage history
      _featureUsageHistory.putIfAbsent(featureName, () => []).add(now);
      
      // Track discovery method
      if (discoveryMethod != null) {
        _featureDiscoveryMethods[discoveryMethod] = 
            (_featureDiscoveryMethods[discoveryMethod] ?? 0) + 1;
      }
      
      // Update engagement score
      _updateEngagementScore(featureName);
      
      // Track user journey
      _trackUserJourney(featureName);
      
      debugPrint('Feature usage tracked: $featureName');
      notifyListeners();
    } catch (e) {
      debugPrint('Error tracking feature usage for $featureName: $e');
    }
  }
  
  /// Update engagement score for a feature
  void _updateEngagementScore(String featureName) {
    final usageCount = _featureUsageCounts[featureName] ?? 0;
    final lastUsage = _lastFeatureUsage[featureName];
    final duration = _featureUsageDurations[featureName];
    
    double score = 0.0;
    
    // Base score from usage count
    score += usageCount * 0.1;
    
    // Recency bonus
    if (lastUsage != null) {
      final daysSinceLastUse = DateTime.now().difference(lastUsage).inDays;
      score += (7 - daysSinceLastUse.clamp(0, 7)) * 0.2;
    }
    
    // Duration bonus
    if (duration != null) {
      score += (duration.inMinutes / 60.0) * 0.3;
    }
    
    _engagementScores[featureName] = score.clamp(0.0, 10.0);
  }
  
  /// Track user journey through features
  void _trackUserJourney(String featureName) {
    const maxJourneyLength = 20;
    final currentSession = DateTime.now().millisecondsSinceEpoch.toString();
    
    _userJourneys.putIfAbsent(currentSession, () => []).add(featureName);
    
    // Keep journey length manageable
    final journey = _userJourneys[currentSession]!;
    if (journey.length > maxJourneyLength) {
      journey.removeRange(0, journey.length - maxJourneyLength);
    }
  }
  
  /// Get underutilized features
  List<String> getUnderutilizedFeatures({double threshold = 2.0}) {
    final underutilized = <String>[];
    
    for (final category in _featureCategories.values) {
      for (final feature in category) {
        final score = _engagementScores[feature] ?? 0.0;
        if (score < threshold) {
          underutilized.add(feature);
        }
      }
    }
    
    return underutilized;
  }
  
  /// Get most popular features
  List<MapEntry<String, int>> getMostPopularFeatures({int limit = 10}) {
    final sortedFeatures = _featureUsageCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedFeatures.take(limit).toList();
  }
  
  /// Get feature usage by category
  Map<String, Map<String, dynamic>> getUsageByCategory() {
    final categoryUsage = <String, Map<String, dynamic>>{};
    
    for (final entry in _featureCategories.entries) {
      final categoryName = entry.key;
      final features = entry.value;
      
      int totalUsage = 0;
      double avgEngagement = 0.0;
      int activeFeatures = 0;
      
      for (final feature in features) {
        final usage = _featureUsageCounts[feature] ?? 0;
        final engagement = _engagementScores[feature] ?? 0.0;
        
        totalUsage += usage;
        avgEngagement += engagement;
        
        if (usage > 0) activeFeatures++;
      }
      
      categoryUsage[categoryName] = {
        'total_usage': totalUsage,
        'average_engagement': features.isNotEmpty ? avgEngagement / features.length : 0.0,
        'active_features': activeFeatures,
        'total_features': features.length,
        'adoption_rate': features.isNotEmpty ? (activeFeatures / features.length) * 100 : 0.0,
      };
    }
    
    return categoryUsage;
  }
  
  /// Get user behavior insights
  Map<String, dynamic> getUserBehaviorInsights() {
    final insights = <String, dynamic>{};
    
    // Feature discovery patterns
    insights['discovery_methods'] = Map.from(_featureDiscoveryMethods);
    
    // Usage patterns
    final usagePatterns = <String, dynamic>{};
    for (final entry in _featureUsageHistory.entries) {
      final feature = entry.key;
      final history = entry.value;
      
      if (history.isNotEmpty) {
        final firstUse = history.first;
        final lastUse = history.last;
        final daysBetween = lastUse.difference(firstUse).inDays;
        
        usagePatterns[feature] = {
          'first_use': firstUse.toIso8601String(),
          'last_use': lastUse.toIso8601String(),
          'usage_span_days': daysBetween,
          'usage_frequency': history.length / (daysBetween + 1),
        };
      }
    }
    insights['usage_patterns'] = usagePatterns;
    
    // Engagement trends
    insights['engagement_scores'] = Map.from(_engagementScores);
    
    return insights;
  }
  
  /// Generate feature recommendations
  List<String> generateFeatureRecommendations({int limit = 5}) {
    final recommendations = <String>[];
    final underutilized = getUnderutilizedFeatures();
    
    // Recommend based on category usage
    final categoryUsage = getUsageByCategory();
    final sortedCategories = categoryUsage.entries.toList()
      ..sort((a, b) => (b.value['adoption_rate'] as double)
          .compareTo(a.value['adoption_rate'] as double));
    
    for (final categoryEntry in sortedCategories) {
      final categoryName = categoryEntry.key;
      final features = _featureCategories[categoryName] ?? [];
      
      for (final feature in features) {
        if (underutilized.contains(feature) && 
            recommendations.length < limit &&
            !recommendations.contains(feature)) {
          recommendations.add(feature);
        }
      }
    }
    
    return recommendations;
  }
  
  /// Generate insights report
  void _generateInsights() {
    try {
      final insights = {
        'timestamp': DateTime.now().toIso8601String(),
        'total_features_used': _featureUsageCounts.length,
        'most_popular': getMostPopularFeatures(limit: 5),
        'underutilized': getUnderutilizedFeatures(),
        'category_usage': getUsageByCategory(),
        'recommendations': generateFeatureRecommendations(),
      };
      
      debugPrint('Generated feature analytics insights: ${insights.length} metrics');
    } catch (e) {
      debugPrint('Error generating insights: $e');
    }
  }
  
  /// Save analytics data to persistent storage
  Future<void> _saveAnalyticsData() async {
    try {
      // Save feature usage data
      await _analyticsBox?.put('feature_usage', {
        'counts': _featureUsageCounts,
        'durations': _featureUsageDurations.map(
          (key, value) => MapEntry(key, value.inMilliseconds),
        ),
        'last_usage': _lastFeatureUsage.map(
          (key, value) => MapEntry(key, value.toIso8601String()),
        ),
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // Save user behavior data
      await _userBehaviorBox?.put('user_behavior', {
        'engagement': _engagementScores,
        'journeys': _userJourneys,
        'discovery_methods': _featureDiscoveryMethods,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error saving analytics data: $e');
    }
  }
  
  /// Cleanup old data to prevent storage bloat
  void _cleanupOldData() {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
      
      // Clean up old usage history
      for (final entry in _featureUsageHistory.entries) {
        entry.value.removeWhere((date) => date.isBefore(cutoffDate));
      }
      
      // Clean up old user journeys
      _userJourneys.removeWhere((session, journey) {
        try {
          final sessionTime = DateTime.fromMillisecondsSinceEpoch(int.parse(session));
          return sessionTime.isBefore(cutoffDate);
        } catch (e) {
          return true; // Remove invalid sessions
        }
      });
      
      debugPrint('Cleaned up old analytics data');
    } catch (e) {
      debugPrint('Error cleaning up old data: $e');
    }
  }
  
  /// Get comprehensive analytics report
  Map<String, dynamic> getAnalyticsReport() {
    return {
      'feature_usage': {
        'counts': Map.from(_featureUsageCounts),
        'durations': _featureUsageDurations.map(
          (key, value) => MapEntry(key, value.inMinutes),
        ),
        'last_usage': _lastFeatureUsage.map(
          (key, value) => MapEntry(key, value.toIso8601String()),
        ),
      },
      'engagement': Map.from(_engagementScores),
      'category_analysis': getUsageByCategory(),
      'user_behavior': getUserBehaviorInsights(),
      'recommendations': generateFeatureRecommendations(),
      'underutilized_features': getUnderutilizedFeatures(),
      'popular_features': getMostPopularFeatures(),
    };
  }
  
  /// Dispose of the service
  @override
  void dispose() {
    _saveAnalyticsData();
    super.dispose();
  }
}
