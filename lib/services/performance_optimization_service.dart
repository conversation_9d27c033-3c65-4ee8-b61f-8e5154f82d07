import 'dart:async';
import 'dart:collection';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../models/hive/cache_entry.dart';
import 'offline_cache_service.dart';
import 'quiz_data_service.dart';

/// Comprehensive performance optimization service for the Moroccan Accounting app
/// Handles image caching, lazy loading, memory management, and performance monitoring
class PerformanceOptimizationService extends ChangeNotifier {
  static const String _performanceBoxName = 'performance_metrics';
  static const String _imagesCacheName = 'images_cache';
  static const String _dataCacheName = 'data_cache';
  
  Box<Map>? _performanceBox;
  Box<Uint8List>? _imagesBox;
  Box<String>? _dataBox;
  
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, Timer> _cacheTimers = {};
  final Map<String, Completer<dynamic>> _loadingOperations = {};
  
  // Performance metrics
  final Map<String, int> _operationCounts = {};
  final Map<String, Duration> _operationDurations = {};
  final List<String> _performanceLogs = [];
  
  // Memory management
  static const int _maxMemoryCacheSize = 50 * 1024 * 1024; // 50MB
  static const int _maxCacheEntries = 1000;
  int _currentMemoryUsage = 0;
  
  // Lazy loading configuration
  final Map<String, bool> _lazyLoadingStates = {};
  final Set<String> _preloadedAssets = {};
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  /// Initialize the performance optimization service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeHiveBoxes();
      await _loadPerformanceMetrics();
      await _preloadCriticalAssets();
      _setupMemoryManagement();
      _isInitialized = true;
      
      debugPrint('PerformanceOptimizationService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing PerformanceOptimizationService: $e');
      rethrow;
    }
  }
  
  /// Initialize Hive boxes for performance data
  Future<void> _initializeHiveBoxes() async {
    try {
      _performanceBox = await Hive.openBox<Map>(_performanceBoxName);
      _imagesBox = await Hive.openBox<Uint8List>(_imagesCacheName);
      _dataBox = await Hive.openBox<String>(_dataCacheName);
    } catch (e) {
      debugPrint('Error opening performance Hive boxes: $e');
      rethrow;
    }
  }
  
  /// Load existing performance metrics
  Future<void> _loadPerformanceMetrics() async {
    try {
      final metrics = _performanceBox?.get('metrics');
      if (metrics != null) {
        _operationCounts.addAll(Map<String, int>.from(metrics['counts'] ?? {}));
        // Load duration data if needed
      }
    } catch (e) {
      debugPrint('Error loading performance metrics: $e');
    }
  }
  
  /// Preload critical assets for better startup performance
  Future<void> _preloadCriticalAssets() async {
    final criticalAssets = [
      'assets/images/logo.png',
      'assets/images/home_background.jpg',
      'assets/icons/quiz_icon.png',
      'assets/icons/calculator_icon.png',
    ];
    
    for (final asset in criticalAssets) {
      try {
        await _preloadAsset(asset);
        _preloadedAssets.add(asset);
      } catch (e) {
        debugPrint('Error preloading asset $asset: $e');
      }
    }
  }
  
  /// Preload a specific asset
  Future<void> _preloadAsset(String assetPath) async {
    try {
      final data = await rootBundle.load(assetPath);
      final bytes = data.buffer.asUint8List();
      await _imagesBox?.put(assetPath, bytes);
    } catch (e) {
      debugPrint('Error preloading asset $assetPath: $e');
    }
  }
  
  /// Setup memory management and cleanup
  void _setupMemoryManagement() {
    // Periodic memory cleanup
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupMemoryCache();
    });
    
    // Monitor memory usage
    Timer.periodic(const Duration(seconds: 30), (_) {
      _monitorMemoryUsage();
    });
  }
  
  /// Get cached image data
  Future<Uint8List?> getCachedImage(String imagePath) async {
    try {
      // Check memory cache first
      if (_memoryCache.containsKey(imagePath)) {
        _recordOperation('cache_hit', imagePath);
        return _memoryCache[imagePath] as Uint8List?;
      }
      
      // Check Hive cache
      final cachedData = await _imagesBox?.get(imagePath);
      if (cachedData != null) {
        _memoryCache[imagePath] = cachedData;
        _recordOperation('disk_cache_hit', imagePath);
        return cachedData;
      }
      
      _recordOperation('cache_miss', imagePath);
      return null;
    } catch (e) {
      debugPrint('Error getting cached image $imagePath: $e');
      return null;
    }
  }
  
  /// Cache image data
  Future<void> cacheImage(String imagePath, Uint8List imageData) async {
    try {
      // Store in memory cache
      _memoryCache[imagePath] = imageData;
      _currentMemoryUsage += imageData.length;
      
      // Store in persistent cache
      await _imagesBox?.put(imagePath, imageData);
      
      _recordOperation('cache_store', imagePath);
      
      // Cleanup if memory usage is too high
      if (_currentMemoryUsage > _maxMemoryCacheSize) {
        _cleanupMemoryCache();
      }
    } catch (e) {
      debugPrint('Error caching image $imagePath: $e');
    }
  }
  
  /// Lazy load data with caching
  Future<T> lazyLoad<T>(
    String key,
    Future<T> Function() loader, {
    Duration? cacheDuration,
    bool forceReload = false,
  }) async {
    // Check if already loading
    if (_loadingOperations.containsKey(key) && !forceReload) {
      return await _loadingOperations[key]!.future as T;
    }
    
    // Check cache first
    if (!forceReload && _memoryCache.containsKey(key)) {
      _recordOperation('lazy_load_cache_hit', key);
      return _memoryCache[key] as T;
    }
    
    // Create loading operation
    final completer = Completer<T>();
    _loadingOperations[key] = completer as Completer<dynamic>;
    
    try {
      final stopwatch = Stopwatch()..start();
      final result = await loader();
      stopwatch.stop();
      
      // Cache the result
      _memoryCache[key] = result;
      
      // Set cache expiration if specified
      if (cacheDuration != null) {
        _cacheTimers[key]?.cancel();
        _cacheTimers[key] = Timer(cacheDuration, () {
          _memoryCache.remove(key);
          _cacheTimers.remove(key);
        });
      }
      
      _recordOperation('lazy_load_success', key, stopwatch.elapsed);
      completer.complete(result);
      return result;
    } catch (e) {
      _recordOperation('lazy_load_error', key);
      completer.completeError(e);
      rethrow;
    } finally {
      _loadingOperations.remove(key);
    }
  }
  
  /// Optimize data loading for large datasets
  Future<List<T>> optimizeDataLoading<T>(
    List<T> data, {
    int batchSize = 50,
    Duration batchDelay = const Duration(milliseconds: 10),
  }) async {
    final optimizedData = <T>[];
    
    for (int i = 0; i < data.length; i += batchSize) {
      final batch = data.skip(i).take(batchSize).toList();
      optimizedData.addAll(batch);
      
      // Small delay to prevent UI blocking
      if (i + batchSize < data.length) {
        await Future.delayed(batchDelay);
      }
    }
    
    return optimizedData;
  }
  
  /// Cleanup memory cache
  void _cleanupMemoryCache() {
    if (_memoryCache.length <= _maxCacheEntries && 
        _currentMemoryUsage <= _maxMemoryCacheSize) {
      return;
    }
    
    // Remove oldest entries first (simple LRU)
    final keysToRemove = <String>[];
    int removedSize = 0;
    
    for (final entry in _memoryCache.entries) {
      if (_memoryCache.length - keysToRemove.length <= _maxCacheEntries ~/ 2) {
        break;
      }
      
      keysToRemove.add(entry.key);
      if (entry.value is Uint8List) {
        removedSize += (entry.value as Uint8List).length;
      }
    }
    
    for (final key in keysToRemove) {
      _memoryCache.remove(key);
      _cacheTimers[key]?.cancel();
      _cacheTimers.remove(key);
    }
    
    _currentMemoryUsage -= removedSize;
    _recordOperation('memory_cleanup', 'removed_${keysToRemove.length}_entries');
  }
  
  /// Monitor memory usage
  void _monitorMemoryUsage() {
    final memoryInfo = {
      'cache_entries': _memoryCache.length,
      'memory_usage_bytes': _currentMemoryUsage,
      'memory_usage_mb': (_currentMemoryUsage / (1024 * 1024)).toStringAsFixed(2),
    };
    
    _recordOperation('memory_monitor', memoryInfo.toString());
  }
  
  /// Record performance operation
  void _recordOperation(String operation, String details, [Duration? duration]) {
    _operationCounts[operation] = (_operationCounts[operation] ?? 0) + 1;
    
    if (duration != null) {
      _operationDurations[operation] = duration;
    }
    
    final logEntry = '${DateTime.now().toIso8601String()}: $operation - $details';
    _performanceLogs.add(logEntry);
    
    // Keep only recent logs
    if (_performanceLogs.length > 1000) {
      _performanceLogs.removeRange(0, _performanceLogs.length - 1000);
    }
  }
  
  /// Get performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'operation_counts': Map.from(_operationCounts),
      'operation_durations': _operationDurations.map(
        (key, value) => MapEntry(key, value.inMilliseconds),
      ),
      'memory_usage': {
        'current_bytes': _currentMemoryUsage,
        'current_mb': (_currentMemoryUsage / (1024 * 1024)).toStringAsFixed(2),
        'cache_entries': _memoryCache.length,
        'max_entries': _maxCacheEntries,
        'max_memory_mb': (_maxMemoryCacheSize / (1024 * 1024)).toStringAsFixed(2),
      },
      'cache_stats': {
        'preloaded_assets': _preloadedAssets.length,
        'active_timers': _cacheTimers.length,
        'loading_operations': _loadingOperations.length,
      },
    };
  }
  
  /// Get recent performance logs
  List<String> getRecentLogs({int limit = 100}) {
    final startIndex = _performanceLogs.length > limit 
        ? _performanceLogs.length - limit 
        : 0;
    return _performanceLogs.sublist(startIndex);
  }
  
  /// Save performance metrics to persistent storage
  Future<void> saveMetrics() async {
    try {
      await _performanceBox?.put('metrics', {
        'counts': _operationCounts,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error saving performance metrics: $e');
    }
  }
  
  /// Clear all caches
  Future<void> clearAllCaches() async {
    try {
      _memoryCache.clear();
      _currentMemoryUsage = 0;
      
      for (final timer in _cacheTimers.values) {
        timer.cancel();
      }
      _cacheTimers.clear();
      
      await _imagesBox?.clear();
      await _dataBox?.clear();
      
      _recordOperation('clear_all_caches', 'success');
    } catch (e) {
      debugPrint('Error clearing caches: $e');
      _recordOperation('clear_all_caches', 'error: $e');
    }
  }
  
  /// Dispose of the service
  @override
  void dispose() {
    for (final timer in _cacheTimers.values) {
      timer.cancel();
    }
    _cacheTimers.clear();
    _loadingOperations.clear();
    
    // Save metrics before disposing
    saveMetrics();
    
    super.dispose();
  }
}
