import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:window_manager/window_manager.dart';
import 'dart:io';
import 'dart:async'; // Added for unawaited futures

import 'package:hive_flutter/hive_flutter.dart'; // Added for Hive

// Import Hive models and adapters
import 'models/hive/user_profile.dart';
import 'models/hive/quiz_attempt.dart';
import 'models/hive/user_progress.dart';
import 'models/hive/active_quiz_state.dart'; // Import quiz active state model
import 'models/hive/cache_entry.dart'; // Import cache entry model
import 'models/exam/exam_attempt.dart'; // Import ExamAttempt model
import 'models/exam/active_exam_state.dart'; // Import exam active state model
import 'models/guide/guide_progress_data.dart'; // Import guide progress model
import 'models/guide/personal_note_data.dart'; // Import personal note model
// Import gamification models

import 'models/adaptive_learning/spaced_repetition_item.dart'; // Import SpacedRepetitionItem model
import 'models/adaptive_learning_models.dart'; // Import adaptive learning models
import 'models/calculators/calculation_history_item.dart'; // Import calculation history model

// Import accessibility and offline models
import 'models/accessibility/accessibility_preferences.dart'; // Import accessibility preferences model

// Import the services and providers
import 'services/user_progress_service.dart';
import 'providers/user_progress_provider.dart';
import 'services/quiz_data_service.dart'; // Import QuizDataService
import 'providers/quiz_data_provider.dart'; // Import QuizDataProvider
import 'services/app_version_service.dart'; // Import AppVersionService

import 'services/guide_progress_service.dart'; // Import guide progress service

// Import accessibility and offline services
import 'services/accessibility_service.dart'; // Import accessibility service
import 'services/offline_cache_service.dart'; // Import offline cache service
import 'services/orientation_service.dart'; // Import orientation service

// Calculator services - will be imported after implementation
// import 'services/calculation_history_service.dart'; // Import calculation history service
// import 'services/financial_ratios_service.dart'; // Import financial ratios service
// import 'services/enhanced_depreciation_service.dart'; // Import enhanced depreciation service
// import 'services/tax_optimization_service.dart'; // Import tax optimization service

import 'screens/home_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/quiz/quiz_screen.dart';
import 'screens/profile/profile_screen.dart'; // Import ProfileScreen
import 'screens/exam/exam_list_screen.dart'; // Import ExamListScreen
// Calculator screens - will be uncommented after implementation
// import 'screens/calculators/financial_ratios_screen.dart';
import 'screens/calculators/enhanced_depreciation_screen.dart';
// import 'screens/calculators/tax_optimization_screen.dart';
// import 'screens/calculators/calculation_history_screen.dart';
import 'screens/gamification/achievements_screen.dart'; // Import gamification screens
import 'screens/gamification/daily_goals_screen.dart'; // Import gamification screens
import 'screens/guides/personal_notes_screen.dart'; // Import notes screens
import 'screens/guides/note_graph_screen.dart'; // Import note graph screen
import 'services/theme_service.dart';
// Custom screens
import 'screens/custom_quiz_builder_screen.dart';
import 'screens/performance_analytics_screen.dart';
import 'screens/spaced_repetition_review_screen.dart';
import 'screens/accessibility/accessibility_settings_screen.dart';
import 'screens/offline/offline_content_screen.dart';
import 'screens/dashboard/unified_dashboard_screen.dart';
import 'screens/settings/advanced_settings_screen.dart';
import 'screens/help/interactive_help_screen.dart';
import 'screens/references_screen.dart';

// Adaptive learning providers and services
import 'providers/spaced_repetition_provider.dart';
import 'services/spaced_repetition_service.dart';
import 'services/adaptive_difficulty_service.dart';
import 'services/adaptive_learning_service.dart';
import 'services/performance_analytics_service.dart';

// Add more adaptive learning imports if needed



void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isWindows) {
    await windowManager.ensureInitialized();

    WindowOptions windowOptions = const WindowOptions(
      size: Size(1280, 720),
      minimumSize: Size(800, 600),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.hidden,
    );

    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }

  // Initialize Hive first
  await Hive.initFlutter();

  // Register accessibility and offline Hive adapters early
  Hive.registerAdapter(AccessibilityPreferencesAdapter());
  Hive.registerAdapter(PreferredNavigationModeAdapter());
  Hive.registerAdapter(CacheEntryAdapter()); // Register cache entry adapter

  // Initialize AccessibilityService before ThemeService to ensure accessibility preferences are available
  AccessibilityService? accessibilityService;
  try {
    accessibilityService = AccessibilityService();
    await accessibilityService.initialize();
    debugPrint('AccessibilityService initialized successfully');
  } catch (e) {
    debugPrint('Error initializing AccessibilityService: $e');
    // Continue with default accessibility settings
  }

  // Initialize OrientationService
  try {
    OrientationService().initialize();
    debugPrint('OrientationService initialized successfully');
  } catch (e) {
    debugPrint('Error initializing OrientationService: $e');
  }

  // Initialize ThemeService with accessibility integration
  final themeService = ThemeService();
  if (accessibilityService != null) {
    accessibilityService.setThemeService(themeService);
  }
  await themeService.init();

  // Initialize OfflineCacheService
  OfflineCacheService? offlineCacheService;
  try {
    offlineCacheService = OfflineCacheService();
    await offlineCacheService.initialize();

    // Preload essential content in background
    unawaited(offlineCacheService.preloadEssentialContent());
    debugPrint('OfflineCacheService initialized successfully');
  } catch (e) {
    debugPrint('Error initializing OfflineCacheService: $e');
    // Continue without offline caching
  }
  
  // Initialize AppVersionService
  final appVersionService = AppVersionService();
  await appVersionService.init();

  // Register remaining Hive Adapters
  Hive.registerAdapter(UserProfileAdapter());
  Hive.registerAdapter(QuizAttemptAdapter());
  Hive.registerAdapter(UserProgressAdapter());
  Hive.registerAdapter(ActiveQuizStateAdapter()); // Register quiz active state adapter
  Hive.registerAdapter(ExamAttemptAdapter()); // Register ExamAttempt adapter
  Hive.registerAdapter(ActiveExamStateAdapter()); // Register exam active state adapter
  Hive.registerAdapter(GuideProgressDataAdapter()); // Register guide progress adapter
  Hive.registerAdapter(PersonalNoteDataAdapter()); // Register personal note adapter
  Hive.registerAdapter(NoteTypeAdapter()); // Register note type adapter

  // Register SpacedRepetitionItem adapter (required for spaced repetition feature)
  Hive.registerAdapter(SpacedRepetitionItemAdapter());

  // Register Calculator History adapters
  Hive.registerAdapter(CalculationHistoryItemAdapter());
  Hive.registerAdapter(CalculatorTypeAdapter());

  // Register Adaptive Learning adapters
  Hive.registerAdapter(UserPerformanceAdapter());
  Hive.registerAdapter(LearningPathAdapter());
  Hive.registerAdapter(WeaknessAreaAdapter());
  Hive.registerAdapter(AchievementAdapter());
  
  // TODO: Register gamification adapters after running build_runner
  // Hive.registerAdapter(DailyGoalDataAdapter());
  // Hive.registerAdapter(DailyGoalTypeAdapter());
  // Hive.registerAdapter(LearningStreakDataAdapter());
  // Hive.registerAdapter(StreakPeriodAdapter());
  // Hive.registerAdapter(SocialShareDataAdapter());
  // Hive.registerAdapter(SocialPlatformAdapter());
  // Hive.registerAdapter(PersonalizedLearningPathAdapter());

  // Open Hive boxes (Handled within the service now)

  // Initialize UserProgressService
  final userProgressService = UserProgressService();
  await userProgressService.init(); 

  // Initialize QuizDataService
  final quizDataService = QuizDataService();
  await quizDataService.loadQuizData(); // Load data on startup

  // Initialize Guide Services
  final guideProgressService = GuideProgressService();
  await guideProgressService.initialize();

  // Initialize Calculator Services - will be uncommented after service implementation
  // final calculationHistoryService = CalculationHistoryService();
  // await calculationHistoryService.initialize();

  // final financialRatiosService = FinancialRatiosService();
  // final enhancedDepreciationService = EnhancedDepreciationService();
  // final taxOptimizationService = TaxOptimizationService();

  // --- Adaptive Learning Services Initialization ---
  // Initialize SpacedRepetitionService and its Hive box
  final spacedRepetitionService = SpacedRepetitionService();
  await spacedRepetitionService.initialize();

  // --- Enhancement: Initialize AdaptiveLearningService and PerformanceAnalyticsService ---
  final adaptiveLearningService = AdaptiveLearningService();
  final performanceAnalyticsService = PerformanceAnalyticsService(
    adaptiveLearningService: adaptiveLearningService,
  );
  await adaptiveLearningService.initialize(
    spacedRepetition: spacedRepetitionService,
    performanceAnalytics: performanceAnalyticsService,
  );

  // Initialize other adaptive learning services if needed
  final adaptiveDifficultyService = AdaptiveDifficultyService(
    adaptiveLearningService: adaptiveLearningService,
    userProgressService: userProgressService,
  );
  // If AdaptiveDifficultyService requires Hive or async init, call it here


  // If QuestionSelectionService requires Hive or async init, call it here

  // --- End Adaptive Learning Initialization ---

  // Ensure proper cleanup on app termination
  if (accessibilityService != null || offlineCacheService != null) {
    // Register app lifecycle callbacks for cleanup
    WidgetsBinding.instance.addObserver(
      _AppLifecycleObserver(
        accessibilityService: accessibilityService,
        offlineCacheService: offlineCacheService,
      ),
    );
  }

  runApp(
    riverpod.ProviderScope(
      overrides: [
        // Provide the initialized service instances
        userProgressServiceProvider.overrideWithValue(userProgressService),
        quizDataServiceProvider.overrideWithValue(quizDataService), // Provide QuizDataService

        // --- Adaptive Learning Provider Overrides ---
        spacedRepetitionServiceProvider.overrideWithValue(spacedRepetitionService),
        // Add overrides for other adaptive learning services if needed
        // adaptiveDifficultyServiceProvider.overrideWithValue(adaptiveDifficultyService),
        // questionSelectionServiceProvider.overrideWithValue(questionSelectionService),
        // --- End Adaptive Learning Provider Overrides ---
      ],
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: themeService),
          ChangeNotifierProvider.value(value: appVersionService), // Provide AppVersionService
          // Provide accessibility service if available
          if (accessibilityService != null)
            ChangeNotifierProvider.value(value: accessibilityService),
          // Consider providing UserProgressService via Provider if needed by non-Riverpod widgets
          // Provider.value(value: userProgressService), 
        ],
        child: MoroccanAccountingApp(
          adaptiveDifficultyService: adaptiveDifficultyService,
          adaptiveLearningService: adaptiveLearningService, // Enhancement: pass adaptiveLearningService
          userProgressService: userProgressService,
          spacedRepetitionService: spacedRepetitionService,
          performanceAnalyticsService: performanceAnalyticsService, // Enhancement: pass performanceAnalyticsService
          accessibilityService: accessibilityService, // Pass accessibility service
          offlineCacheService: offlineCacheService, // Pass offline cache service
        ),
      ),
    ),
  );
}

class MoroccanAccountingApp extends StatelessWidget {
  final dynamic adaptiveDifficultyService;
  final dynamic adaptiveLearningService; // Enhancement
  final dynamic userProgressService;
  final dynamic spacedRepetitionService;
  final dynamic performanceAnalyticsService; // Enhancement
  final AccessibilityService? accessibilityService; // Accessibility service
  final OfflineCacheService? offlineCacheService; // Offline cache service

  const MoroccanAccountingApp({
    super.key,
    required this.adaptiveDifficultyService,
    required this.adaptiveLearningService,
    required this.userProgressService,
    required this.spacedRepetitionService,
    required this.performanceAnalyticsService,
    this.accessibilityService,
    this.offlineCacheService,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        final robotoTextTheme = GoogleFonts.robotoTextTheme(
          Theme.of(context).textTheme,
        );

        return MaterialApp(
          title: 'Comptabilité Marocaine',
          theme: themeService.theme.copyWith(
            textTheme: robotoTextTheme,
          ),
          initialRoute: '/',
          routes: {
            '/': (context) => const SplashScreen(),
            '/home': (context) => const HomeScreen(),
            '/quiz': (context) => const QuizScreen(),
            '/profile': (context) => const ProfileScreen(), // Add profile route
            '/exam_list': (context) => const ExamListScreen(), // Add exam list route
            // Calculator routes - will be uncommented after implementation
            // '/financial-ratios': (context) => const FinancialRatiosScreen(),
            '/enhanced_depreciation': (context) => const EnhancedDepreciationScreen(),
            // '/tax-optimization': (context) => const TaxOptimizationScreen(),
            // '/calculation-history': (context) => const CalculationHistoryScreen(),
            '/achievements': (context) => const AchievementsScreen(), // Add gamification routes
            '/daily_goals': (context) => const DailyGoalsScreen(),
            // Notes routes
            '/personal_notes': (context) => const PersonalNotesScreen(),
            '/note_graph': (context) => const NoteGraphScreen(),
            // Custom screens
            '/custom_quiz_builder': (context) => const CustomQuizBuilderScreen(),
            '/performance_analytics': (context) => PerformanceAnalyticsScreen(
              adaptiveLearningService: adaptiveLearningService,
              userProgressService: userProgressService,
              spacedRepetitionService: spacedRepetitionService,
              performanceAnalyticsService: performanceAnalyticsService,
            ),
            '/spaced_repetition_review': (context) => SpacedRepetitionReviewScreen(
              spacedRepetitionService: spacedRepetitionService,
            ),
            // Accessibility and offline routes
            '/accessibility_settings': (context) => const AccessibilitySettingsScreen(),
            '/offline_content': (context) => const OfflineContentScreen(),
            // Dashboard routes
            '/unified_dashboard': (context) => const UnifiedDashboardScreen(),
            // Settings routes
            '/advanced_settings': (context) => const AdvancedSettingsScreen(),
            // Help routes
            '/interactive_help': (context) => const InteractiveHelpScreen(),
            // References route
            '/references': (context) => const ReferencesScreen(),
          },
        );
      },
    );
  }
}

/// App lifecycle observer for proper cleanup of services
class _AppLifecycleObserver extends WidgetsBindingObserver {
  final AccessibilityService? accessibilityService;
  final OfflineCacheService? offlineCacheService;

  _AppLifecycleObserver({
    this.accessibilityService,
    this.offlineCacheService,
  });

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.detached:
        // App is being terminated, cleanup services
        _cleanup();
        break;
      case AppLifecycleState.paused:
        // App is paused, save any pending data
        _saveState();
        break;
      case AppLifecycleState.resumed:
        // App is resumed, refresh cache if needed
        _refreshServices();
        break;
      default:
        break;
    }
  }

  void _cleanup() {
    try {
      accessibilityService?.dispose();
      offlineCacheService?.dispose();
      debugPrint('Services cleaned up successfully');
    } catch (e) {
      debugPrint('Error during service cleanup: $e');
    }
  }

  void _saveState() {
    try {
      // Save accessibility preferences and cache state
      // This is handled automatically by the services
      debugPrint('Service state saved');
    } catch (e) {
      debugPrint('Error saving service state: $e');
    }
  }

  void _refreshServices() {
    try {
      // Refresh offline cache if needed
      if (offlineCacheService != null) {
        unawaited(offlineCacheService!.clearExpiredCache());
      }
      debugPrint('Services refreshed');
    } catch (e) {
      debugPrint('Error refreshing services: $e');
    }
  }
}

/// Helper function for unawaited futures
void unawaited(Future<void> future) {
  // Intentionally not awaiting the future
  future.catchError((error) {
    debugPrint('Unawaited future error: $error');
  });
}
