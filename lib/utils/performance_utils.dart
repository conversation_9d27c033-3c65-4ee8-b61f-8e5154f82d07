import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:flutter/foundation.dart';

import 'calculation_utils.dart';
import 'excel_export_utils.dart';

/// Enhanced utility functions with performance optimizations
/// Adds memoization, efficient algorithms, and performance monitoring
class PerformanceUtils {
  // Memoization cache
  static final Map<String, dynamic> _memoCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);
  
  // Performance monitoring
  static final Map<String, List<Duration>> _performanceMetrics = {};
  static final Map<String, int> _callCounts = {};
  
  /// Memoized function wrapper with automatic cache expiry
  static T memoize<T>(String key, T Function() computation) {
    // Check if cached value exists and is not expired
    if (_memoCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && 
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _memoCache[key] as T;
      } else {
        // Remove expired cache entry
        _memoCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    
    // Compute and cache the result
    final stopwatch = Stopwatch()..start();
    final result = computation();
    stopwatch.stop();
    
    _memoCache[key] = result;
    _cacheTimestamps[key] = DateTime.now();
    
    // Track performance
    _trackPerformance('memoize_$key', stopwatch.elapsed);
    
    return result;
  }
  
  /// Optimized sorting with different algorithms based on data size
  static List<T> optimizedSort<T>(
    List<T> list,
    int Function(T, T) compare, {
    bool inPlace = false,
  }) {
    final targetList = inPlace ? list : List<T>.from(list);
    
    if (targetList.length <= 1) return targetList;
    
    final stopwatch = Stopwatch()..start();
    
    // Choose algorithm based on size
    if (targetList.length < 50) {
      // Insertion sort for small arrays
      _insertionSort(targetList, compare);
    } else if (targetList.length < 1000) {
      // Quick sort for medium arrays
      _quickSort(targetList, 0, targetList.length - 1, compare);
    } else {
      // Merge sort for large arrays (stable and predictable)
      targetList.sort(compare);
    }
    
    stopwatch.stop();
    _trackPerformance('optimized_sort_${targetList.length}', stopwatch.elapsed);
    
    return targetList;
  }
  
  /// Insertion sort implementation
  static void _insertionSort<T>(List<T> list, int Function(T, T) compare) {
    for (int i = 1; i < list.length; i++) {
      final key = list[i];
      int j = i - 1;
      
      while (j >= 0 && compare(list[j], key) > 0) {
        list[j + 1] = list[j];
        j--;
      }
      list[j + 1] = key;
    }
  }
  
  /// Quick sort implementation
  static void _quickSort<T>(
    List<T> list,
    int low,
    int high,
    int Function(T, T) compare,
  ) {
    if (low < high) {
      final pi = _partition(list, low, high, compare);
      _quickSort(list, low, pi - 1, compare);
      _quickSort(list, pi + 1, high, compare);
    }
  }
  
  /// Partition function for quick sort
  static int _partition<T>(
    List<T> list,
    int low,
    int high,
    int Function(T, T) compare,
  ) {
    final pivot = list[high];
    int i = low - 1;
    
    for (int j = low; j < high; j++) {
      if (compare(list[j], pivot) <= 0) {
        i++;
        final temp = list[i];
        list[i] = list[j];
        list[j] = temp;
      }
    }
    
    final temp = list[i + 1];
    list[i + 1] = list[high];
    list[high] = temp;
    
    return i + 1;
  }
  
  /// Efficient filtering with early termination
  static List<T> optimizedFilter<T>(
    List<T> list,
    bool Function(T) predicate, {
    int? maxResults,
  }) {
    final stopwatch = Stopwatch()..start();
    final result = <T>[];
    
    for (final item in list) {
      if (predicate(item)) {
        result.add(item);
        if (maxResults != null && result.length >= maxResults) {
          break;
        }
      }
    }
    
    stopwatch.stop();
    _trackPerformance('optimized_filter_${list.length}', stopwatch.elapsed);
    
    return result;
  }
  
  /// Efficient data transformation with batching
  static Future<List<R>> optimizedTransform<T, R>(
    List<T> list,
    R Function(T) transform, {
    int batchSize = 100,
    Duration batchDelay = const Duration(microseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    final result = <R>[];
    
    for (int i = 0; i < list.length; i += batchSize) {
      final batch = list.skip(i).take(batchSize);
      result.addAll(batch.map(transform));
      
      // Small delay to prevent UI blocking
      if (i + batchSize < list.length) {
        await Future.delayed(batchDelay);
      }
    }
    
    stopwatch.stop();
    _trackPerformance('optimized_transform_${list.length}', stopwatch.elapsed);
    
    return result;
  }
  
  /// Optimized search with different algorithms
  static List<T> optimizedSearch<T>(
    List<T> list,
    bool Function(T) predicate, {
    SearchAlgorithm algorithm = SearchAlgorithm.linear,
    int? maxResults,
  }) {
    final stopwatch = Stopwatch()..start();
    List<T> result;
    
    switch (algorithm) {
      case SearchAlgorithm.linear:
        result = _linearSearch(list, predicate, maxResults);
        break;
      case SearchAlgorithm.binary:
        // Binary search requires sorted list and specific predicate
        result = _binarySearchRange(list, predicate, maxResults);
        break;
      case SearchAlgorithm.jump:
        result = _jumpSearch(list, predicate, maxResults);
        break;
    }
    
    stopwatch.stop();
    _trackPerformance('optimized_search_${algorithm.name}_${list.length}', stopwatch.elapsed);
    
    return result;
  }
  
  /// Linear search implementation
  static List<T> _linearSearch<T>(
    List<T> list,
    bool Function(T) predicate,
    int? maxResults,
  ) {
    final result = <T>[];
    
    for (final item in list) {
      if (predicate(item)) {
        result.add(item);
        if (maxResults != null && result.length >= maxResults) {
          break;
        }
      }
    }
    
    return result;
  }
  
  /// Binary search range implementation
  static List<T> _binarySearchRange<T>(
    List<T> list,
    bool Function(T) predicate,
    int? maxResults,
  ) {
    // Simplified binary search - in practice would need more specific implementation
    return _linearSearch(list, predicate, maxResults);
  }
  
  /// Jump search implementation
  static List<T> _jumpSearch<T>(
    List<T> list,
    bool Function(T) predicate,
    int? maxResults,
  ) {
    final result = <T>[];
    final jumpSize = sqrt(list.length).floor();
    
    for (int i = 0; i < list.length; i += jumpSize) {
      final endIndex = min(i + jumpSize, list.length);
      
      for (int j = i; j < endIndex; j++) {
        if (predicate(list[j])) {
          result.add(list[j]);
          if (maxResults != null && result.length >= maxResults) {
            return result;
          }
        }
      }
    }
    
    return result;
  }
  
  /// Optimized data aggregation
  static Map<K, V> optimizedGroupBy<T, K, V>(
    List<T> list,
    K Function(T) keySelector,
    V Function(List<T>) valueSelector,
  ) {
    final stopwatch = Stopwatch()..start();
    final groups = <K, List<T>>{};
    
    // Group items
    for (final item in list) {
      final key = keySelector(item);
      groups.putIfAbsent(key, () => []).add(item);
    }
    
    // Apply value selector
    final result = groups.map((key, items) => MapEntry(key, valueSelector(items)));
    
    stopwatch.stop();
    _trackPerformance('optimized_group_by_${list.length}', stopwatch.elapsed);
    
    return result;
  }
  
  /// Efficient data deduplication
  static List<T> optimizedDeduplication<T>(
    List<T> list, {
    Object? Function(T)? keySelector,
  }) {
    final stopwatch = Stopwatch()..start();
    
    if (keySelector != null) {
      final seen = <Object?>{};
      final result = <T>[];
      
      for (final item in list) {
        final key = keySelector(item);
        if (seen.add(key)) {
          result.add(item);
        }
      }
      
      stopwatch.stop();
      _trackPerformance('optimized_dedup_key_${list.length}', stopwatch.elapsed);
      return result;
    } else {
      final result = LinkedHashSet<T>.from(list).toList();
      stopwatch.stop();
      _trackPerformance('optimized_dedup_${list.length}', stopwatch.elapsed);
      return result;
    }
  }
  
  /// Optimized calculation caching
  static double cachedCalculation(
    String calculationType,
    Map<String, dynamic> parameters,
    double Function() calculation,
  ) {
    final cacheKey = '${calculationType}_${parameters.hashCode}';
    
    return memoize(cacheKey, calculation);
  }
  
  /// Batch processing with progress tracking
  static Future<List<R>> batchProcess<T, R>(
    List<T> items,
    Future<R> Function(T) processor, {
    int batchSize = 10,
    Duration batchDelay = const Duration(milliseconds: 100),
    void Function(double progress)? onProgress,
  }) async {
    final stopwatch = Stopwatch()..start();
    final results = <R>[];
    
    for (int i = 0; i < items.length; i += batchSize) {
      final batch = items.skip(i).take(batchSize).toList();
      final batchResults = await Future.wait(
        batch.map(processor),
      );
      
      results.addAll(batchResults);
      
      // Report progress
      if (onProgress != null) {
        final progress = (i + batch.length) / items.length;
        onProgress(progress);
      }
      
      // Delay between batches
      if (i + batchSize < items.length) {
        await Future.delayed(batchDelay);
      }
    }
    
    stopwatch.stop();
    _trackPerformance('batch_process_${items.length}', stopwatch.elapsed);
    
    return results;
  }
  
  /// Track performance metrics
  static void _trackPerformance(String operation, Duration duration) {
    _performanceMetrics.putIfAbsent(operation, () => []).add(duration);
    _callCounts[operation] = (_callCounts[operation] ?? 0) + 1;
    
    // Keep metrics manageable
    final metrics = _performanceMetrics[operation]!;
    if (metrics.length > 100) {
      metrics.removeRange(0, metrics.length - 100);
    }
  }
  
  /// Get performance statistics
  static Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};
    
    for (final operation in _performanceMetrics.keys) {
      final durations = _performanceMetrics[operation]!;
      final callCount = _callCounts[operation] ?? 0;
      
      if (durations.isNotEmpty) {
        final totalMs = durations.fold<int>(0, (sum, d) => sum + d.inMicroseconds);
        final avgMs = totalMs / durations.length / 1000;
        final minMs = durations.map((d) => d.inMicroseconds).reduce(min) / 1000;
        final maxMs = durations.map((d) => d.inMicroseconds).reduce(max) / 1000;
        
        stats[operation] = {
          'call_count': callCount,
          'avg_duration_ms': avgMs.toStringAsFixed(2),
          'min_duration_ms': minMs.toStringAsFixed(2),
          'max_duration_ms': maxMs.toStringAsFixed(2),
          'total_duration_ms': (totalMs / 1000).toStringAsFixed(2),
        };
      }
    }
    
    return stats;
  }
  
  /// Clear performance cache
  static void clearCache() {
    _memoCache.clear();
    _cacheTimestamps.clear();
  }
  
  /// Clear performance metrics
  static void clearMetrics() {
    _performanceMetrics.clear();
    _callCounts.clear();
  }
  
  /// Get cache statistics
  static Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int expiredCount = 0;
    
    for (final timestamp in _cacheTimestamps.values) {
      if (now.difference(timestamp) >= _cacheExpiry) {
        expiredCount++;
      }
    }
    
    return {
      'total_cached_items': _memoCache.length,
      'expired_items': expiredCount,
      'cache_hit_potential': _memoCache.length - expiredCount,
      'cache_expiry_minutes': _cacheExpiry.inMinutes,
    };
  }
}

/// Search algorithm options
enum SearchAlgorithm {
  linear,
  binary,
  jump,
}
