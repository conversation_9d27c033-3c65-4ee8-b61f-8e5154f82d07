import 'package:flutter/material.dart';




/// Unified dashboard that showcases all app capabilities
/// Provides comprehensive overview of learning progress, features, and achievements
class UnifiedDashboardScreen extends StatefulWidget {
  const UnifiedDashboardScreen({super.key});

  @override
  State<UnifiedDashboardScreen> createState() => _UnifiedDashboardScreenState();
}

class _UnifiedDashboardScreenState extends State<UnifiedDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  final List<DashboardTab> _tabs = [
    DashboardTab(
      title: 'Vue d\'ensemble',
      icon: Icons.dashboard,
      color: Colors.blue,
    ),
    DashboardTab(
      title: 'Apprentissage',
      icon: Icons.school,
      color: Colors.green,
    ),
    DashboardTab(
      title: 'Analytics',
      icon: Icons.analytics,
      color: Colors.purple,
    ),
    DashboardTab(
      title: 'Gamification',
      icon: Icons.emoji_events,
      color: Colors.orange,
    ),
    DashboardTab(
      title: 'Outils',
      icon: Icons.build,
      color: Colors.teal,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedIndex = _tabController.index;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de Bord Unifié'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: colorScheme.primary,
              unselectedLabelColor: colorScheme.onSurfaceVariant,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              tabs: _tabs.map((tab) => Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(tab.icon, size: 18),
                    const SizedBox(width: 8),
                    Text(tab.title),
                  ],
                ),
              )).toList(),
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildLearningTab(),
          _buildAnalyticsTab(),
          _buildGamificationTab(),
          _buildToolsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildQuickStatsGrid(),
          const SizedBox(height: 16),
          _buildRecentActivityCard(),
          const SizedBox(height: 16),
          _buildFeaturedFeaturesCard(),
        ],
      ),
    );
  }

  Widget _buildLearningTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLearningProgressCard(),
          const SizedBox(height: 16),
          _buildSpacedRepetitionCard(),
          const SizedBox(height: 16),
          _buildAdaptiveLearningCard(),
          const SizedBox(height: 16),
          _buildQuizPerformanceCard(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Performance analytics placeholder
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Analytics de Performance',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text('Tableau de bord des analytics de performance'),
                  const SizedBox(height: 12),
                  LinearProgressIndicator(value: 0.75),
                  const SizedBox(height: 8),
                  const Text('Progression globale: 75%'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGamificationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Gamification dashboard placeholder
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Gamification',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text('Tableau de bord de gamification'),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.emoji_events, color: Colors.orange),
                      const SizedBox(width: 8),
                      const Text('Achievements: 8/15'),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.local_fire_department, color: Colors.red),
                      const SizedBox(width: 8),
                      const Text('Streak: 5 jours'),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCalculatorsCard(),
          const SizedBox(height: 16),
          _buildNotesCard(),
          const SizedBox(height: 16),
          _buildExportToolsCard(),
          const SizedBox(height: 16),
          _buildAccessibilityCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.primary.withValues(alpha: 0.1),
              colorScheme.secondary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.waving_hand,
                  color: colorScheme.primary,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bienvenue dans votre espace d\'apprentissage',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Découvrez toutes les fonctionnalités avancées de l\'application',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsGrid() {
    final stats = [
      {'title': 'Quiz Complétés', 'value': '24', 'icon': Icons.quiz, 'color': Colors.blue},
      {'title': 'Temps d\'Étude', 'value': '12h', 'icon': Icons.schedule, 'color': Colors.green},
      {'title': 'Achievements', 'value': '8', 'icon': Icons.emoji_events, 'color': Colors.orange},
      {'title': 'Streak', 'value': '5j', 'icon': Icons.local_fire_department, 'color': Colors.red},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(
          title: stat['title'] as String,
          value: stat['value'] as String,
          icon: stat['icon'] as IconData,
          color: stat['color'] as Color,
        );
      },
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const Spacer(),
                Text(
                  value,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Activité Récente',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              'Quiz Fiscalité complété',
              '85% de réussite',
              Icons.quiz,
              Colors.green,
            ),
            _buildActivityItem(
              'Calculateur TVA utilisé',
              'Calcul de 15 opérations',
              Icons.calculate,
              Colors.blue,
            ),
            _buildActivityItem(
              'Note collaborative créée',
              'Partagée avec 3 personnes',
              Icons.note_add,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String subtitle, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.bodyMedium),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedFeaturesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fonctionnalités à Découvrir',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem(
              'Répétition Espacée',
              'Optimisez votre mémorisation',
              Icons.psychology,
              Colors.purple,
              () => Navigator.pushNamed(context, '/spaced_repetition_review'),
            ),
            _buildFeatureItem(
              'Analytics Avancées',
              'Analysez vos performances',
              Icons.analytics,
              Colors.green,
              () => Navigator.pushNamed(context, '/performance_analytics'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: Theme.of(context).textTheme.bodyMedium),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  // Placeholder methods for other tabs
  Widget _buildLearningProgressCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Learning Progress')));
  Widget _buildSpacedRepetitionCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Spaced Repetition')));
  Widget _buildAdaptiveLearningCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Adaptive Learning')));
  Widget _buildQuizPerformanceCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Quiz Performance')));
  Widget _buildCalculatorsCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Calculators')));
  Widget _buildNotesCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Notes')));
  Widget _buildExportToolsCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Export Tools')));
  Widget _buildAccessibilityCard() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Accessibility')));

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

class DashboardTab {
  final String title;
  final IconData icon;
  final Color color;

  DashboardTab({
    required this.title,
    required this.icon,
    required this.color,
  });
}
