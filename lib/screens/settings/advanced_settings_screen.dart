import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../services/theme_service.dart';
import '../../services/accessibility_service.dart';

/// Advanced settings screen for sophisticated feature configuration
/// Exposes configuration options for spaced repetition, adaptive learning, and more
class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen> {
  // Spaced repetition settings
  double _spacedRepetitionInterval = 1.0;
  double _difficultyMultiplier = 1.3;
  bool _adaptiveIntervals = true;
  
  // Adaptive learning settings
  bool _enableAdaptiveDifficulty = true;
  double _learningRate = 0.1;
  int _performanceWindow = 10;
  
  // Notification settings
  bool _studyReminders = true;
  bool _achievementNotifications = true;
  bool _weeklyReports = true;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 19, minute: 0);
  
  // Data sync settings
  bool _autoSync = true;
  bool _syncOnWifiOnly = true;
  int _syncInterval = 30; // minutes
  
  // Performance settings
  bool _enableAnimations = true;
  bool _enableHapticFeedback = true;
  bool _preloadContent = true;
  int _cacheSize = 100; // MB
  
  // Experimental features
  bool _betaFeatures = false;
  bool _advancedAnalytics = false;
  bool _aiRecommendations = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres Avancés'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionHeader('Répétition Espacée'),
          _buildSpacedRepetitionSettings(),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Apprentissage Adaptatif'),
          _buildAdaptiveLearningSettings(),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Notifications'),
          _buildNotificationSettings(),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Synchronisation'),
          _buildSyncSettings(),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Performance'),
          _buildPerformanceSettings(),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Fonctionnalités Expérimentales'),
          _buildExperimentalSettings(),
          const SizedBox(height: 24),
          
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSpacedRepetitionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildSliderSetting(
              'Intervalle Initial (jours)',
              _spacedRepetitionInterval,
              0.5,
              7.0,
              (value) => setState(() => _spacedRepetitionInterval = value),
              divisions: 13,
            ),
            _buildSliderSetting(
              'Multiplicateur de Difficulté',
              _difficultyMultiplier,
              1.0,
              3.0,
              (value) => setState(() => _difficultyMultiplier = value),
              divisions: 20,
            ),
            _buildSwitchSetting(
              'Intervalles Adaptatifs',
              'Ajuste automatiquement les intervalles selon vos performances',
              _adaptiveIntervals,
              (value) => setState(() => _adaptiveIntervals = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdaptiveLearningSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildSwitchSetting(
              'Difficulté Adaptative',
              'Ajuste la difficulté selon vos performances',
              _enableAdaptiveDifficulty,
              (value) => setState(() => _enableAdaptiveDifficulty = value),
            ),
            _buildSliderSetting(
              'Taux d\'Apprentissage',
              _learningRate,
              0.01,
              0.5,
              (value) => setState(() => _learningRate = value),
              divisions: 49,
            ),
            _buildSliderSetting(
              'Fenêtre de Performance',
              _performanceWindow.toDouble(),
              5.0,
              50.0,
              (value) => setState(() => _performanceWindow = value.round()),
              divisions: 45,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildSwitchSetting(
              'Rappels d\'Étude',
              'Notifications quotidiennes pour étudier',
              _studyReminders,
              (value) => setState(() => _studyReminders = value),
            ),
            if (_studyReminders)
              ListTile(
                title: const Text('Heure du Rappel'),
                subtitle: Text(_reminderTime.format(context)),
                trailing: const Icon(Icons.access_time),
                onTap: _selectReminderTime,
              ),
            _buildSwitchSetting(
              'Notifications d\'Achievements',
              'Alertes pour nouveaux accomplissements',
              _achievementNotifications,
              (value) => setState(() => _achievementNotifications = value),
            ),
            _buildSwitchSetting(
              'Rapports Hebdomadaires',
              'Résumé de vos progrès chaque semaine',
              _weeklyReports,
              (value) => setState(() => _weeklyReports = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildSwitchSetting(
              'Synchronisation Automatique',
              'Synchronise vos données automatiquement',
              _autoSync,
              (value) => setState(() => _autoSync = value),
            ),
            _buildSwitchSetting(
              'WiFi Uniquement',
              'Synchronise seulement sur WiFi',
              _syncOnWifiOnly,
              (value) => setState(() => _syncOnWifiOnly = value),
            ),
            _buildSliderSetting(
              'Intervalle de Sync (minutes)',
              _syncInterval.toDouble(),
              5.0,
              120.0,
              (value) => setState(() => _syncInterval = value.round()),
              divisions: 23,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildSwitchSetting(
              'Animations',
              'Active les animations de l\'interface',
              _enableAnimations,
              (value) => setState(() => _enableAnimations = value),
            ),
            _buildSwitchSetting(
              'Retour Haptique',
              'Vibrations lors des interactions',
              _enableHapticFeedback,
              (value) => setState(() => _enableHapticFeedback = value),
            ),
            _buildSwitchSetting(
              'Préchargement du Contenu',
              'Charge le contenu en arrière-plan',
              _preloadContent,
              (value) => setState(() => _preloadContent = value),
            ),
            _buildSliderSetting(
              'Taille du Cache (MB)',
              _cacheSize.toDouble(),
              50.0,
              500.0,
              (value) => setState(() => _cacheSize = value.round()),
              divisions: 45,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperimentalSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Ces fonctionnalités sont expérimentales et peuvent être instables',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchSetting(
              'Fonctionnalités Beta',
              'Active les nouvelles fonctionnalités en test',
              _betaFeatures,
              (value) => setState(() => _betaFeatures = value),
            ),
            _buildSwitchSetting(
              'Analytics Avancées',
              'Collecte de données détaillées pour améliorer l\'app',
              _advancedAnalytics,
              (value) => setState(() => _advancedAnalytics = value),
            ),
            _buildSwitchSetting(
              'Recommandations IA',
              'Suggestions personnalisées basées sur l\'IA',
              _aiRecommendations,
              (value) => setState(() => _aiRecommendations = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchSetting(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSliderSetting(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged, {
    int? divisions,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title),
            Text(
              divisions != null ? value.round().toString() : value.toStringAsFixed(2),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _saveSettings,
            child: const Text('Sauvegarder les Paramètres'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _resetToDefaults,
            child: const Text('Réinitialiser par Défaut'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _exportSettings,
            child: const Text('Exporter les Paramètres'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectReminderTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _reminderTime,
    );
    if (picked != null && picked != _reminderTime) {
      setState(() {
        _reminderTime = picked;
      });
    }
  }

  void _saveSettings() {
    // Implement settings save logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Paramètres sauvegardés avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetToDefaults() {
    setState(() {
      _spacedRepetitionInterval = 1.0;
      _difficultyMultiplier = 1.3;
      _adaptiveIntervals = true;
      _enableAdaptiveDifficulty = true;
      _learningRate = 0.1;
      _performanceWindow = 10;
      _studyReminders = true;
      _achievementNotifications = true;
      _weeklyReports = true;
      _reminderTime = const TimeOfDay(hour: 19, minute: 0);
      _autoSync = true;
      _syncOnWifiOnly = true;
      _syncInterval = 30;
      _enableAnimations = true;
      _enableHapticFeedback = true;
      _preloadContent = true;
      _cacheSize = 100;
      _betaFeatures = false;
      _advancedAnalytics = false;
      _aiRecommendations = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Paramètres réinitialisés par défaut'),
      ),
    );
  }

  void _exportSettings() {
    // Implement settings export logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Paramètres exportés vers les téléchargements'),
      ),
    );
  }
}
