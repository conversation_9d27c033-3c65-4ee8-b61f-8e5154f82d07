import 'package:hive/hive.dart';

part 'cache_entry.g.dart';

/// Cache entry metadata model
@HiveType(typeId: 122)
class CacheEntry extends HiveObject {
  @HiveField(0)
  final String cacheKey;

  @HiveField(1)
  final DateTime createdAt;

  @HiveField(2)
  final DateTime lastAccessed;

  @HiveField(3)
  final DateTime expiresAt;

  @HiveField(4)
  final int sizeBytes;

  @HiveField(5)
  final String contentType;

  @HiveField(6)
  final bool isCompressed;

  @HiveField(7)
  final String? etag;

  @HiveField(8)
  final Map<String, dynamic>? metadata;

  CacheEntry({
    required this.cacheKey,
    required this.createdAt,
    required this.lastAccessed,
    required this.expiresAt,
    required this.sizeBytes,
    required this.contentType,
    this.isCompressed = false,
    this.etag,
    this.metadata,
  });

  CacheEntry copyWith({
    DateTime? lastAccessed,
    DateTime? expiresAt,
    int? sizeBytes,
    String? etag,
    Map<String, dynamic>? metadata,
  }) {
    return CacheEntry(
      cacheKey: cacheKey,
      createdAt: createdAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      expiresAt: expiresAt ?? this.expiresAt,
      sizeBytes: sizeBytes ?? this.sizeBytes,
      contentType: contentType,
      isCompressed: isCompressed,
      etag: etag ?? this.etag,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isValid => !isExpired;
}
