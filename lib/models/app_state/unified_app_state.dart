import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../hive/user_progress.dart';
import '../accessibility/accessibility_preferences.dart';

/// Unified app state management system coordinating all existing features
/// Implements centralized state for user preferences, learning progress, and cross-feature data sharing
class UnifiedAppState extends ChangeNotifier {
  static const String _stateBoxName = 'unified_app_state';
  static const String _preferencesBoxName = 'app_preferences';
  static const String _sessionBoxName = 'session_data';
  
  Box<Map>? _stateBox;
  Box<Map>? _preferencesBox;
  Box<Map>? _sessionBox;
  
  // Core app state
  AppTheme _currentTheme = AppTheme.system;
  String _currentLanguage = 'fr';
  bool _isFirstLaunch = true;
  String _appVersion = '1.0.0';
  
  // User preferences
  final Map<String, dynamic> _userPreferences = {};
  AccessibilityPreferences? _accessibilityPreferences;
  
  // Learning progress state
  UserProgress? _userProgress;
  final Map<String, double> _featureProgress = {};
  final Map<String, DateTime> _lastActivity = {};
  
  // Cross-feature data sharing
  final Map<String, dynamic> _sharedData = {};
  final Map<String, List<String>> _featureConnections = {};
  
  // Session management
  String? _currentSessionId;
  DateTime? _sessionStartTime;
  final Map<String, dynamic> _sessionData = {};
  
  // Synchronization state
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  final List<String> _pendingSyncItems = [];
  
  // Conflict resolution
  final Map<String, ConflictResolution> _conflictResolutions = {};
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  // Getters
  AppTheme get currentTheme => _currentTheme;
  String get currentLanguage => _currentLanguage;
  bool get isFirstLaunch => _isFirstLaunch;
  String get appVersion => _appVersion;
  UserProgress? get userProgress => _userProgress;
  AccessibilityPreferences? get accessibilityPreferences => _accessibilityPreferences;
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get currentSessionId => _currentSessionId;
  
  /// Initialize the unified app state
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeHiveBoxes();
      await _loadAppState();
      await _loadUserPreferences();
      await _loadSessionData();
      _startNewSession();
      _isInitialized = true;
      
      debugPrint('UnifiedAppState initialized successfully');
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing UnifiedAppState: $e');
      rethrow;
    }
  }
  
  /// Initialize Hive boxes for state management
  Future<void> _initializeHiveBoxes() async {
    try {
      _stateBox = await Hive.openBox<Map>(_stateBoxName);
      _preferencesBox = await Hive.openBox<Map>(_preferencesBoxName);
      _sessionBox = await Hive.openBox<Map>(_sessionBoxName);
    } catch (e) {
      debugPrint('Error opening unified state Hive boxes: $e');
      rethrow;
    }
  }
  
  /// Load app state from persistent storage
  Future<void> _loadAppState() async {
    try {
      final state = _stateBox?.get('app_state');
      if (state != null) {
        _currentTheme = AppTheme.values.firstWhere(
          (theme) => theme.name == state['theme'],
          orElse: () => AppTheme.system,
        );
        _currentLanguage = state['language'] ?? 'fr';
        _isFirstLaunch = state['is_first_launch'] ?? true;
        _appVersion = state['app_version'] ?? '1.0.0';
        _lastSyncTime = state['last_sync_time'] != null
            ? DateTime.parse(state['last_sync_time'])
            : null;
      }
    } catch (e) {
      debugPrint('Error loading app state: $e');
    }
  }
  
  /// Load user preferences from persistent storage
  Future<void> _loadUserPreferences() async {
    try {
      final preferences = _preferencesBox?.get('user_preferences');
      if (preferences != null) {
        _userPreferences.addAll(Map<String, dynamic>.from(preferences));
      }
      
      // Load accessibility preferences
      final accessibilityData = _preferencesBox?.get('accessibility');
      if (accessibilityData != null) {
        // Convert map to AccessibilityPreferences object
        // This would need proper implementation based on the actual model
      }
    } catch (e) {
      debugPrint('Error loading user preferences: $e');
    }
  }
  
  /// Load session data
  Future<void> _loadSessionData() async {
    try {
      final sessionData = _sessionBox?.get('current_session');
      if (sessionData != null) {
        _currentSessionId = sessionData['session_id'];
        _sessionStartTime = sessionData['start_time'] != null
            ? DateTime.parse(sessionData['start_time'])
            : null;
        _sessionData.addAll(Map<String, dynamic>.from(sessionData['data'] ?? {}));
      }
    } catch (e) {
      debugPrint('Error loading session data: $e');
    }
  }
  
  /// Start a new user session
  void _startNewSession() {
    _currentSessionId = DateTime.now().millisecondsSinceEpoch.toString();
    _sessionStartTime = DateTime.now();
    _sessionData.clear();
    
    _saveSessionData();
  }
  
  /// Update app theme
  Future<void> updateTheme(AppTheme theme) async {
    if (_currentTheme != theme) {
      _currentTheme = theme;
      await _saveAppState();
      notifyListeners();
    }
  }
  
  /// Update app language
  Future<void> updateLanguage(String language) async {
    if (_currentLanguage != language) {
      _currentLanguage = language;
      await _saveAppState();
      notifyListeners();
    }
  }
  
  /// Mark first launch as completed
  Future<void> completeFirstLaunch() async {
    if (_isFirstLaunch) {
      _isFirstLaunch = false;
      await _saveAppState();
      notifyListeners();
    }
  }
  
  /// Update user progress
  Future<void> updateUserProgress(UserProgress progress) async {
    _userProgress = progress;
    await _saveUserData();
    notifyListeners();
  }
  
  /// Update accessibility preferences
  Future<void> updateAccessibilityPreferences(AccessibilityPreferences preferences) async {
    _accessibilityPreferences = preferences;
    await _saveUserPreferences();
    notifyListeners();
  }
  
  /// Set user preference
  Future<void> setUserPreference(String key, dynamic value) async {
    _userPreferences[key] = value;
    await _saveUserPreferences();
    notifyListeners();
  }
  
  /// Get user preference
  T? getUserPreference<T>(String key) {
    return _userPreferences[key] as T?;
  }
  
  /// Share data between features
  void shareData(String key, dynamic data, {List<String>? targetFeatures}) {
    _sharedData[key] = data;
    
    if (targetFeatures != null) {
      _featureConnections[key] = targetFeatures;
    }
    
    notifyListeners();
  }
  
  /// Get shared data
  T? getSharedData<T>(String key) {
    return _sharedData[key] as T?;
  }
  
  /// Update feature progress
  void updateFeatureProgress(String featureId, double progress) {
    _featureProgress[featureId] = progress;
    _lastActivity[featureId] = DateTime.now();
    notifyListeners();
  }
  
  /// Get feature progress
  double getFeatureProgress(String featureId) {
    return _featureProgress[featureId] ?? 0.0;
  }
  
  /// Get overall progress
  double getOverallProgress() {
    if (_featureProgress.isEmpty) return 0.0;
    
    final totalProgress = _featureProgress.values.reduce((a, b) => a + b);
    return totalProgress / _featureProgress.length;
  }
  
  /// Add session data
  void addSessionData(String key, dynamic value) {
    _sessionData[key] = value;
    _saveSessionData();
  }
  
  /// Get session data
  T? getSessionData<T>(String key) {
    return _sessionData[key] as T?;
  }
  
  /// Start synchronization
  Future<void> startSync() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    notifyListeners();
    
    try {
      await _performSync();
      _lastSyncTime = DateTime.now();
      _pendingSyncItems.clear();
    } catch (e) {
      debugPrint('Error during sync: $e');
    } finally {
      _isSyncing = false;
      await _saveAppState();
      notifyListeners();
    }
  }
  
  /// Perform actual synchronization
  Future<void> _performSync() async {
    // Implementation would sync with cloud storage or other devices
    await Future.delayed(const Duration(seconds: 2)); // Simulate sync
    debugPrint('Sync completed');
  }
  
  /// Add item to pending sync
  void addPendingSyncItem(String item) {
    if (!_pendingSyncItems.contains(item)) {
      _pendingSyncItems.add(item);
    }
  }
  
  /// Resolve data conflict
  void resolveConflict(String key, ConflictResolution resolution) {
    _conflictResolutions[key] = resolution;
    
    // Apply resolution based on strategy
    switch (resolution.strategy) {
      case ConflictStrategy.useLocal:
        // Keep local data
        break;
      case ConflictStrategy.useRemote:
        // Use remote data
        _sharedData[key] = resolution.remoteValue;
        break;
      case ConflictStrategy.merge:
        // Merge data (implementation depends on data type)
        _mergeData(key, resolution.localValue, resolution.remoteValue);
        break;
      case ConflictStrategy.askUser:
        // This would trigger a UI dialog
        break;
    }
    
    notifyListeners();
  }
  
  /// Merge conflicting data
  void _mergeData(String key, dynamic localValue, dynamic remoteValue) {
    // Simple merge strategy - in practice would be more sophisticated
    if (localValue is Map && remoteValue is Map) {
      final merged = Map.from(localValue);
      merged.addAll(remoteValue);
      _sharedData[key] = merged;
    } else {
      // Use most recent based on timestamp or other criteria
      _sharedData[key] = remoteValue;
    }
  }
  
  /// Save app state to persistent storage
  Future<void> _saveAppState() async {
    try {
      await _stateBox?.put('app_state', {
        'theme': _currentTheme.name,
        'language': _currentLanguage,
        'is_first_launch': _isFirstLaunch,
        'app_version': _appVersion,
        'last_sync_time': _lastSyncTime?.toIso8601String(),
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error saving app state: $e');
    }
  }
  
  /// Save user preferences
  Future<void> _saveUserPreferences() async {
    try {
      await _preferencesBox?.put('user_preferences', _userPreferences);
      
      if (_accessibilityPreferences != null) {
        // Save accessibility preferences
        await _preferencesBox?.put('accessibility', {
          // Convert AccessibilityPreferences to map
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('Error saving user preferences: $e');
    }
  }
  
  /// Save user data
  Future<void> _saveUserData() async {
    try {
      if (_userProgress != null) {
        // Save user progress - implementation depends on UserProgress model
        await _stateBox?.put('user_progress', {
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('Error saving user data: $e');
    }
  }
  
  /// Save session data
  Future<void> _saveSessionData() async {
    try {
      await _sessionBox?.put('current_session', {
        'session_id': _currentSessionId,
        'start_time': _sessionStartTime?.toIso8601String(),
        'data': _sessionData,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error saving session data: $e');
    }
  }
  
  /// Get app state summary
  Map<String, dynamic> getStateSummary() {
    return {
      'theme': _currentTheme.name,
      'language': _currentLanguage,
      'is_first_launch': _isFirstLaunch,
      'app_version': _appVersion,
      'is_syncing': _isSyncing,
      'last_sync_time': _lastSyncTime?.toIso8601String(),
      'session_id': _currentSessionId,
      'session_start_time': _sessionStartTime?.toIso8601String(),
      'feature_progress_count': _featureProgress.length,
      'shared_data_count': _sharedData.length,
      'pending_sync_items': _pendingSyncItems.length,
      'overall_progress': getOverallProgress(),
    };
  }
  
  /// Clear all state (for testing or reset)
  Future<void> clearAllState() async {
    try {
      await _stateBox?.clear();
      await _preferencesBox?.clear();
      await _sessionBox?.clear();
      
      // Reset in-memory state
      _currentTheme = AppTheme.system;
      _currentLanguage = 'fr';
      _isFirstLaunch = true;
      _userPreferences.clear();
      _sharedData.clear();
      _featureProgress.clear();
      _lastActivity.clear();
      _sessionData.clear();
      _pendingSyncItems.clear();
      _conflictResolutions.clear();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all state: $e');
    }
  }
  
  /// Dispose of the state manager
  @override
  void dispose() {
    _saveAppState();
    _saveUserPreferences();
    _saveSessionData();
    super.dispose();
  }
}

/// App theme options
enum AppTheme {
  light,
  dark,
  system,
}

/// Conflict resolution strategies
enum ConflictStrategy {
  useLocal,
  useRemote,
  merge,
  askUser,
}

/// Conflict resolution data
class ConflictResolution {
  final ConflictStrategy strategy;
  final dynamic localValue;
  final dynamic remoteValue;
  final DateTime timestamp;

  ConflictResolution({
    required this.strategy,
    required this.localValue,
    required this.remoteValue,
    required this.timestamp,
  });
}
